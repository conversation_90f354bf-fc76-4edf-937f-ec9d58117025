#!/usr/bin/env python3
"""
Setup script for AI Team Orchestrator
Compatible with older pip versions
"""

from setuptools import setup, find_packages
import os

# Read the README file
def read_readme():
    try:
        with open("README.md", "r", encoding="utf-8") as fh:
            return fh.read()
    except FileNotFoundError:
        return "AI Team Orchestrator - Multi-Agent Complaint Management System"

# Read requirements from pyproject.toml dependencies
def get_requirements():
    requirements = [
        "email-validator>=2.2.0",
        "flask>=3.1.1",
        "flask-sqlalchemy>=3.1.1",
        "groq>=0.26.0",
        "gunicorn>=23.0.0",
        "openai>=1.82.1",
        "mysql-connector-python>=8.0.33",
        "cx-oracle>=8.3.0",
        "werkzeug>=3.1.3",
        "langgraph>=0.0.55",
        "langchain>=0.1.0",
        "langchain-community>=0.0.20",
        "imaplib2>=3.6",
        "email-reply-parser>=0.5.12",
        "python-dotenv>=1.0.0",
        "schedule>=1.2.0",
        "smtplib2>=0.2.1",
    ]
    return requirements

setup(
    name="ai-team-orchestrator",
    version="0.1.0",
    description="AI Team Orchestrator - Multi-Agent Complaint Management System for HR Recruitment",
    long_description=read_readme(),
    long_description_content_type="text/markdown",
    author="AI Team Orchestrator",
    author_email="<EMAIL>",
    url="https://github.com/your-repo/ai-team-orchestrator",
    packages=find_packages(),
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Developers",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.11",
        "Programming Language :: Python :: 3.12",
        "Topic :: Software Development :: Libraries :: Python Modules",
        "Topic :: Office/Business :: Human Resources",
        "Topic :: Communications :: Email",
        "Topic :: Scientific/Engineering :: Artificial Intelligence",
    ],
    python_requires=">=3.11",
    install_requires=get_requirements(),
    extras_require={
        "dev": [
            "pytest>=7.0.0",
            "pytest-cov>=4.0.0",
            "black>=23.0.0",
            "flake8>=6.0.0",
            "mypy>=1.0.0",
        ],
        "oracle": [
            "cx-oracle>=8.3.0",
        ],
        "mysql": [
            "mysql-connector-python>=8.0.33",
        ],
    },
    entry_points={
        "console_scripts": [
            "ai-orchestrator=start:main",
            "ai-test=test_system:main",
        ],
    },
    include_package_data=True,
    package_data={
        "": ["*.html", "*.css", "*.js", "*.md", "*.txt", "*.env*"],
    },
    zip_safe=False,
)
