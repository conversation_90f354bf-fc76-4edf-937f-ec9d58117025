from flask import render_template, request, jsonify, redirect, url_for, flash
from app import app
from agents import complaint_processor
from database import db_manager
from datetime import datetime
import logging

logger = logging.getLogger(__name__)

@app.route('/')
def index():
    """Main dashboard showing all complaints"""
    try:
        complaints = db_manager.get_all_complaints()
        
        # Sort by creation date (newest first)
        complaints.sort(key=lambda x: x.get('created_at', ''), reverse=True)
        
        # Calculate statistics
        stats = {
            'total': len(complaints),
            'pending_validation': len([c for c in complaints if c.get('status') == 'pending_validation']),
            'auto_resolved': len([c for c in complaints if c.get('status') == 'auto_resolved']),
            'hr_validated': len([c for c in complaints if c.get('status') == 'hr_validated'])
        }
        
        return render_template('index.html', complaints=complaints, stats=stats)
    
    except Exception as e:
        logger.error(f"Error loading dashboard: {str(e)}")
        flash(f"Error loading complaints: {str(e)}", 'error')
        return render_template('index.html', complaints=[], stats={'total': 0, 'pending_validation': 0, 'auto_resolved': 0, 'hr_validated': 0})

@app.route('/complaint/<complaint_id>')
def complaint_detail(complaint_id):
    """Show detailed view of a specific complaint"""
    try:
        complaint = db_manager.get_complaint(complaint_id)
        if not complaint:
            flash('Complaint not found', 'error')
            return redirect(url_for('index'))
        
        return render_template('complaint_detail.html', complaint=complaint)
    
    except Exception as e:
        logger.error(f"Error loading complaint {complaint_id}: {str(e)}")
        flash(f"Error loading complaint: {str(e)}", 'error')
        return redirect(url_for('index'))

@app.route('/submit_complaint', methods=['POST'])
def submit_complaint():
    """Process new complaint submission"""
    try:
        # Get form data
        email_content = request.form.get('email_content', '').strip()
        sender_email = request.form.get('sender_email', '').strip()
        subject = request.form.get('subject', '').strip()
        
        # Validate input
        if not email_content or not sender_email:
            flash('Email content and sender email are required', 'error')
            return redirect(url_for('index'))
        
        if not subject:
            subject = "No subject"
        
        # Process complaint through agent system
        complaint_id = complaint_processor.process_complaint(email_content, sender_email, subject)
        
        flash(f'Complaint {complaint_id} processed successfully', 'success')
        return redirect(url_for('complaint_detail', complaint_id=complaint_id))
    
    except Exception as e:
        logger.error(f"Error processing complaint: {str(e)}")
        flash(f"Error processing complaint: {str(e)}", 'error')
        return redirect(url_for('index'))

@app.route('/validate_complaint', methods=['POST'])
def validate_complaint():
    """HR validation of complaint actions"""
    try:
        complaint_id = request.form.get('complaint_id')
        approved_actions = request.form.getlist('approved_actions')
        hr_comments = request.form.get('hr_comments', '').strip()
        
        if not complaint_id:
            flash('Invalid complaint ID', 'error')
            return redirect(url_for('index'))
        
        # Process validation
        success = complaint_processor.validate_and_execute(
            complaint_id, approved_actions, hr_comments
        )
        
        if success:
            flash('Complaint validated and actions executed', 'success')
        else:
            flash('Error validating complaint', 'error')
        
        return redirect(url_for('complaint_detail', complaint_id=complaint_id))
    
    except Exception as e:
        logger.error(f"Error validating complaint: {str(e)}")
        flash(f"Error validating complaint: {str(e)}", 'error')
        return redirect(url_for('index'))

@app.route('/api/complaints')
def api_complaints():
    """API endpoint to get all complaints"""
    try:
        complaints = db_manager.get_all_complaints()
        return jsonify({
            'success': True,
            'complaints': complaints
        })
    except Exception as e:
        logger.error(f"API error: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/complaint/<complaint_id>')
def api_complaint_detail(complaint_id):
    """API endpoint to get specific complaint"""
    try:
        complaint = db_manager.get_complaint(complaint_id)
        if not complaint:
            return jsonify({
                'success': False,
                'error': 'Complaint not found'
            }), 404
        
        return jsonify({
            'success': True,
            'complaint': complaint
        })
    except Exception as e:
        logger.error(f"API error: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/candidates')
def api_candidates():
    """API endpoint to get all candidates"""
    try:
        candidates = db_manager.get_all_candidates()
        return jsonify({
            'success': True,
            'candidates': candidates
        })
    except Exception as e:
        logger.error(f"API error: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/reject_complaint/<complaint_id>', methods=['POST'])
def reject_complaint(complaint_id):
    """Reject a complaint validation"""
    try:
        hr_comments = request.form.get('rejection_reason', '').strip()
        
        success = db_manager.update_complaint(complaint_id, {
            'status': 'rejected',
            'hr_comments': hr_comments,
            'rejected_at': datetime.now().isoformat()
        })
        
        if success:
            flash('Complaint rejected', 'info')
        else:
            flash('Error rejecting complaint', 'error')
        
        return redirect(url_for('complaint_detail', complaint_id=complaint_id))
    
    except Exception as e:
        logger.error(f"Error rejecting complaint: {str(e)}")
        flash(f"Error rejecting complaint: {str(e)}", 'error')
        return redirect(url_for('index'))

# Error handlers
@app.errorhandler(404)
def not_found_error(error):
    return render_template('index.html', complaints=[], stats={'total': 0, 'pending_validation': 0, 'auto_resolved': 0, 'hr_validated': 0}), 404

@app.errorhandler(500)
def internal_error(error):
    logger.error(f"Internal server error: {str(error)}")
    flash('An internal error occurred', 'error')
    return render_template('index.html', complaints=[], stats={'total': 0, 'pending_validation': 0, 'auto_resolved': 0, 'hr_validated': 0}), 500
