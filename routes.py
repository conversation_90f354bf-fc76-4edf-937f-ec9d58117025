from flask import render_template, request, jsonify, redirect, url_for, flash
from app import app
from agents import complaint_processor
from langgraph_orchestrator import langgraph_orchestrator
from email_monitor import email_monitor
from email_service import email_service
from database import db_manager
from datetime import datetime
import logging

logger = logging.getLogger(__name__)

@app.route('/')
def index():
    """Main dashboard showing all complaints"""
    try:
        complaints = db_manager.get_all_complaints()
        
        # Sort by creation date (newest first)
        complaints.sort(key=lambda x: x.get('created_at', ''), reverse=True)
        
        # Calculate statistics
        stats = {
            'total': len(complaints),
            'pending_validation': len([c for c in complaints if c.get('status') == 'pending_validation']),
            'auto_resolved': len([c for c in complaints if c.get('status') == 'auto_resolved']),
            'hr_validated': len([c for c in complaints if c.get('status') == 'hr_validated'])
        }
        
        return render_template('index.html', complaints=complaints, stats=stats)
    
    except Exception as e:
        logger.error(f"Error loading dashboard: {str(e)}")
        flash(f"Error loading complaints: {str(e)}", 'error')
        return render_template('index.html', complaints=[], stats={'total': 0, 'pending_validation': 0, 'auto_resolved': 0, 'hr_validated': 0})

@app.route('/complaint/<complaint_id>')
def complaint_detail(complaint_id):
    """Show detailed view of a specific complaint"""
    try:
        complaint = db_manager.get_complaint(complaint_id)
        if not complaint:
            flash('Complaint not found', 'error')
            return redirect(url_for('index'))
        
        return render_template('complaint_detail.html', complaint=complaint)
    
    except Exception as e:
        logger.error(f"Error loading complaint {complaint_id}: {str(e)}")
        flash(f"Error loading complaint: {str(e)}", 'error')
        return redirect(url_for('index'))

@app.route('/submit_complaint', methods=['POST'])
def submit_complaint():
    """Process new complaint submission"""
    try:
        # Get form data
        email_content = request.form.get('email_content', '').strip()
        sender_email = request.form.get('sender_email', '').strip()
        subject = request.form.get('subject', '').strip()
        use_langgraph = request.form.get('use_langgraph', 'true').lower() == 'true'

        # Validate input
        if not email_content or not sender_email:
            flash('Email content and sender email are required', 'error')
            return redirect(url_for('index'))

        if not subject:
            subject = "No subject"

        # Process complaint through selected system
        if use_langgraph:
            complaint_id = langgraph_orchestrator.process_complaint(email_content, sender_email, subject)
        else:
            complaint_id = complaint_processor.process_complaint(email_content, sender_email, subject)

        flash(f'Complaint {complaint_id} processed successfully', 'success')
        return redirect(url_for('complaint_detail', complaint_id=complaint_id))

    except Exception as e:
        logger.error(f"Error processing complaint: {str(e)}")
        flash(f"Error processing complaint: {str(e)}", 'error')
        return redirect(url_for('index'))

@app.route('/validate_complaint', methods=['POST'])
def validate_complaint():
    """HR validation of complaint actions"""
    try:
        complaint_id = request.form.get('complaint_id')
        approved_actions = request.form.getlist('approved_actions')
        hr_comments = request.form.get('hr_comments', '').strip()
        
        if not complaint_id:
            flash('Invalid complaint ID', 'error')
            return redirect(url_for('index'))
        
        # Process validation
        success = complaint_processor.validate_and_execute(
            complaint_id, approved_actions, hr_comments
        )
        
        if success:
            flash('Complaint validated and actions executed', 'success')
        else:
            flash('Error validating complaint', 'error')
        
        return redirect(url_for('complaint_detail', complaint_id=complaint_id))
    
    except Exception as e:
        logger.error(f"Error validating complaint: {str(e)}")
        flash(f"Error validating complaint: {str(e)}", 'error')
        return redirect(url_for('index'))

@app.route('/api/complaints')
def api_complaints():
    """API endpoint to get all complaints"""
    try:
        complaints = db_manager.get_all_complaints()
        return jsonify({
            'success': True,
            'complaints': complaints
        })
    except Exception as e:
        logger.error(f"API error: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/complaint/<complaint_id>')
def api_complaint_detail(complaint_id):
    """API endpoint to get specific complaint"""
    try:
        complaint = db_manager.get_complaint(complaint_id)
        if not complaint:
            return jsonify({
                'success': False,
                'error': 'Complaint not found'
            }), 404
        
        return jsonify({
            'success': True,
            'complaint': complaint
        })
    except Exception as e:
        logger.error(f"API error: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/candidates')
def api_candidates():
    """API endpoint to get all candidates"""
    try:
        candidates = db_manager.get_all_candidates()
        return jsonify({
            'success': True,
            'candidates': candidates
        })
    except Exception as e:
        logger.error(f"API error: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/reject_complaint/<complaint_id>', methods=['POST'])
def reject_complaint(complaint_id):
    """Reject a complaint validation"""
    try:
        hr_comments = request.form.get('rejection_reason', '').strip()

        success = db_manager.update_complaint(complaint_id, {
            'status': 'rejected',
            'hr_comments': hr_comments,
            'rejected_at': datetime.now().isoformat()
        })

        if success:
            flash('Complaint rejected', 'info')
        else:
            flash('Error rejecting complaint', 'error')

        return redirect(url_for('complaint_detail', complaint_id=complaint_id))

    except Exception as e:
        logger.error(f"Error rejecting complaint: {str(e)}")
        flash(f"Error rejecting complaint: {str(e)}", 'error')
        return redirect(url_for('index'))

# Email monitoring routes
@app.route('/email_monitor')
def email_monitor_dashboard():
    """Email monitoring dashboard"""
    try:
        status = email_monitor.get_status()
        return render_template('email_monitor.html', status=status)
    except Exception as e:
        logger.error(f"Error loading email monitor dashboard: {str(e)}")
        flash(f"Error loading email monitor: {str(e)}", 'error')
        return redirect(url_for('index'))

@app.route('/email_monitor/start', methods=['POST'])
def start_email_monitor():
    """Start email monitoring"""
    try:
        email_monitor.start_monitoring()
        flash('Email monitoring started', 'success')
    except Exception as e:
        logger.error(f"Error starting email monitor: {str(e)}")
        flash(f"Error starting email monitor: {str(e)}", 'error')
    return redirect(url_for('email_monitor_dashboard'))

@app.route('/email_monitor/stop', methods=['POST'])
def stop_email_monitor():
    """Stop email monitoring"""
    try:
        email_monitor.stop_monitoring()
        flash('Email monitoring stopped', 'info')
    except Exception as e:
        logger.error(f"Error stopping email monitor: {str(e)}")
        flash(f"Error stopping email monitor: {str(e)}", 'error')
    return redirect(url_for('email_monitor_dashboard'))

@app.route('/email_monitor/check', methods=['POST'])
def check_emails_now():
    """Manually check for new emails"""
    try:
        complaints = email_monitor.check_now()
        if complaints:
            flash(f'Processed {len(complaints)} new complaints: {", ".join(complaints)}', 'success')
        else:
            flash('No new complaint emails found', 'info')
    except Exception as e:
        logger.error(f"Error checking emails: {str(e)}")
        flash(f"Error checking emails: {str(e)}", 'error')
    return redirect(url_for('email_monitor_dashboard'))

@app.route('/api/email_monitor/status')
def api_email_monitor_status():
    """API endpoint for email monitor status"""
    try:
        status = email_monitor.get_status()
        return jsonify({
            'success': True,
            'status': status
        })
    except Exception as e:
        logger.error(f"API error: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/send_response/<complaint_id>', methods=['POST'])
def send_response(complaint_id):
    """Send response email for a complaint"""
    try:
        complaint = db_manager.get_complaint(complaint_id)
        if not complaint:
            flash('Complaint not found', 'error')
            return redirect(url_for('index'))

        # Get custom response or use suggested response
        custom_response = request.form.get('custom_response', '').strip()
        if custom_response:
            response_content = custom_response
        else:
            suggested_response = complaint.get('suggested_response', {})
            response_content = suggested_response.get('content', '')

        if not response_content:
            flash('No response content provided', 'error')
            return redirect(url_for('complaint_detail', complaint_id=complaint_id))

        # Extract original email data
        email_data = complaint.get('email_data', {})
        original_email = {
            'sender': email_data.get('sender_email', ''),
            'subject': email_data.get('subject', ''),
            'message_id': email_data.get('message_id', '')
        }

        # Send response
        success = email_service.send_response_email(original_email, response_content)

        if success:
            # Update complaint status
            db_manager.update_complaint(complaint_id, {
                'status': 'response_sent',
                'response_sent_at': datetime.now().isoformat(),
                'final_response': response_content
            })
            flash('Response sent successfully', 'success')
        else:
            flash('Failed to send response', 'error')

        return redirect(url_for('complaint_detail', complaint_id=complaint_id))

    except Exception as e:
        logger.error(f"Error sending response: {str(e)}")
        flash(f"Error sending response: {str(e)}", 'error')
        return redirect(url_for('complaint_detail', complaint_id=complaint_id))

# Error handlers
@app.errorhandler(404)
def not_found_error(error):
    return render_template('index.html', complaints=[], stats={'total': 0, 'pending_validation': 0, 'auto_resolved': 0, 'hr_validated': 0}), 404

@app.errorhandler(500)
def internal_error(error):
    logger.error(f"Internal server error: {str(error)}")
    flash('An internal error occurred', 'error')
    return render_template('index.html', complaints=[], stats={'total': 0, 'pending_validation': 0, 'auto_resolved': 0, 'hr_validated': 0}), 500
