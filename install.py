#!/usr/bin/env python3
"""
Installation script for AI Team Orchestrator
Handles dependency installation with fallbacks
"""

import subprocess
import sys
import os

def run_command(command, description):
    """Run a command and handle errors"""
    print(f"🔄 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed:")
        print(f"   Error: {e.stderr}")
        return False

def install_dependencies():
    """Install dependencies with multiple fallback methods"""
    
    # Method 1: Try upgrading pip first
    print("📦 Installing AI Team Orchestrator Dependencies")
    print("=" * 50)
    
    # Upgrade pip
    if not run_command(f"{sys.executable} -m pip install --upgrade pip", "Upgrading pip"):
        print("⚠️  Pip upgrade failed, continuing with current version")
    
    # Method 2: Try requirements.txt
    if run_command(f"{sys.executable} -m pip install -r requirements.txt", "Installing from requirements.txt"):
        return True
    
    # Method 3: Install core dependencies manually
    print("🔄 Fallback: Installing core dependencies manually...")
    
    core_deps = [
        "flask>=3.1.1",
        "python-dotenv>=1.0.0",
        "groq>=0.26.0",
        "mysql-connector-python>=8.0.33",
        "email-validator>=2.2.0",
    ]
    
    success = True
    for dep in core_deps:
        if not run_command(f"{sys.executable} -m pip install '{dep}'", f"Installing {dep}"):
            success = False
    
    # Method 4: Install optional dependencies
    optional_deps = [
        "langgraph",
        "langchain",
        "langchain-community",
        "schedule",
        "email-reply-parser",
    ]
    
    print("\n🔄 Installing optional dependencies...")
    for dep in optional_deps:
        run_command(f"{sys.executable} -m pip install {dep}", f"Installing {dep} (optional)")
    
    return success

def create_directories():
    """Create necessary directories"""
    directories = ["logs", "static/css", "static/js"]
    
    for directory in directories:
        if not os.path.exists(directory):
            os.makedirs(directory)
            print(f"📁 Created directory: {directory}")

def check_installation():
    """Check if installation was successful"""
    print("\n🔍 Checking installation...")
    
    try:
        import flask
        print("✅ Flask installed")
    except ImportError:
        print("❌ Flask not installed")
        return False
    
    try:
        import groq
        print("✅ Groq installed")
    except ImportError:
        print("❌ Groq not installed")
        return False
    
    try:
        import mysql.connector
        print("✅ MySQL connector installed")
    except ImportError:
        print("❌ MySQL connector not installed")
        return False
    
    try:
        from dotenv import load_dotenv
        print("✅ Python-dotenv installed")
    except ImportError:
        print("❌ Python-dotenv not installed")
        return False
    
    return True

def main():
    """Main installation process"""
    print("🚀 AI Team Orchestrator Installation")
    print("=" * 50)
    
    # Check Python version
    if sys.version_info < (3, 11):
        print("❌ Python 3.11+ required")
        print(f"   Current version: {sys.version}")
        return False
    
    print(f"✅ Python version: {sys.version}")
    
    # Create directories
    create_directories()
    
    # Install dependencies
    if not install_dependencies():
        print("\n⚠️  Some dependencies failed to install")
        print("💡 You can try installing them manually:")
        print("   pip install flask python-dotenv groq mysql-connector-python")
    
    # Check installation
    if check_installation():
        print("\n🎉 Installation completed successfully!")
        print("\n📋 Next steps:")
        print("1. Configure your .env file:")
        print("   cp .env.example .env")
        print("   # Edit .env with your settings")
        print("\n2. Start the application:")
        print("   python start.py")
        print("\n3. Access the dashboard:")
        print("   http://localhost:5000")
        return True
    else:
        print("\n❌ Installation verification failed")
        print("💡 Please check the error messages above and install missing dependencies manually")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
