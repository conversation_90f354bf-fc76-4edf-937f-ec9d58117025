/* Custom styles for AI Complaint Management System */

:root {
    --primary-color: 210 100% 50%;
    --success-color: 142 76% 36%;
    --warning-color: 45 100% 51%;
    --danger-color: 354 70% 54%;
    --info-color: 199 89% 48%;
    --dark-color: 210 11% 15%;
    --light-color: 210 17% 98%;
    --muted-color: 210 8% 62%;
}

/* Enhanced card styling */
.card {
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.3);
    transition: box-shadow 0.15s ease-in-out;
}

.card:hover {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.4);
}

/* Email content styling */
.email-content {
    font-family: 'Courier New', monospace;
    white-space: pre-wrap;
    word-wrap: break-word;
    max-height: 300px;
    overflow-y: auto;
    border: 1px solid var(--bs-border-color);
}

.response-content {
    white-space: pre-wrap;
    word-wrap: break-word;
    font-family: system-ui, -apple-system, sans-serif;
    line-height: 1.6;
}

/* Progress bar enhancements */
.progress {
    height: 1rem;
    border-radius: 0.375rem;
}

.progress-bar {
    transition: width 0.6s ease;
}

/* Badge styling */
.badge {
    font-weight: 500;
    letter-spacing: 0.025em;
}

/* Table enhancements */
.table-hover tbody tr:hover {
    background-color: rgba(255, 255, 255, 0.05);
}

.table code {
    background-color: rgba(255, 255, 255, 0.1);
    padding: 0.2rem 0.4rem;
    border-radius: 0.25rem;
    font-size: 0.875em;
    transition: background-color 0.15s ease;
}

.table code:hover {
    background-color: rgba(255, 255, 255, 0.2);
}

/* Form enhancements */
.form-control:focus {
    border-color: hsl(var(--primary-color));
    box-shadow: 0 0 0 0.2rem hsla(var(--primary-color), 0.25);
}

.form-check-input:checked {
    background-color: hsl(var(--primary-color));
    border-color: hsl(var(--primary-color));
}

.form-check-input:focus {
    border-color: hsl(var(--primary-color));
    box-shadow: 0 0 0 0.2rem hsla(var(--primary-color), 0.25);
}

/* Alert enhancements */
.alert {
    border: none;
    border-radius: 0.5rem;
}

.alert-dismissible .btn-close {
    padding: 0.75rem 1rem;
}

/* Button enhancements */
.btn {
    border-radius: 0.375rem;
    font-weight: 500;
    transition: all 0.15s ease-in-out;
}

.btn-outline-secondary:hover {
    transform: translateY(-1px);
}

/* Navigation enhancements */
.navbar-brand {
    font-weight: 600;
    font-size: 1.25rem;
}

/* Status indicators */
.status-indicator {
    position: relative;
    display: inline-block;
}

.status-indicator::before {
    content: '';
    position: absolute;
    top: 50%;
    left: -0.75rem;
    width: 0.5rem;
    height: 0.5rem;
    border-radius: 50%;
    transform: translateY(-50%);
}

.status-indicator.status-pending::before {
    background-color: hsl(var(--warning-color));
}

.status-indicator.status-success::before {
    background-color: hsl(var(--success-color));
}

.status-indicator.status-danger::before {
    background-color: hsl(var(--danger-color));
}

/* Animated elements */
@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 hsla(var(--primary-color), 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px hsla(var(--primary-color), 0);
    }
    100% {
        box-shadow: 0 0 0 0 hsla(var(--primary-color), 0);
    }
}

.pulse {
    animation: pulse 2s infinite;
}

/* Dashboard stats cards */
.card.bg-primary,
.card.bg-success,
.card.bg-warning,
.card.bg-info {
    color: white;
}

.card.bg-primary .card-body i,
.card.bg-success .card-body i,
.card.bg-warning .card-body i,
.card.bg-info .card-body i {
    opacity: 0.8;
}

/* Responsive improvements */
@media (max-width: 768px) {
    .table-responsive {
        font-size: 0.875rem;
    }
    
    .card-body {
        padding: 1rem;
    }
    
    .btn {
        font-size: 0.875rem;
        padding: 0.375rem 0.75rem;
    }
    
    .navbar-brand {
        font-size: 1.1rem;
    }
}

/* Loading states */
.loading-spinner {
    border: 2px solid #f3f3f3;
    border-top: 2px solid hsl(var(--primary-color));
    border-radius: 50%;
    width: 1rem;
    height: 1rem;
    animation: spin 1s linear infinite;
    display: inline-block;
    margin-right: 0.5rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Character count styling */
.character-count {
    display: block;
    text-align: right;
    margin-top: 0.25rem;
}

/* Enhanced validation styles */
.form-control.is-invalid {
    border-color: hsl(var(--danger-color));
    box-shadow: 0 0 0 0.2rem hsla(var(--danger-color), 0.25);
}

.invalid-feedback {
    display: block;
    font-size: 0.875rem;
    color: hsl(var(--danger-color));
    margin-top: 0.25rem;
}

/* Action cards styling */
.form-check.p-3.border {
    background-color: rgba(255, 255, 255, 0.02);
    transition: background-color 0.15s ease;
}

.form-check.p-3.border:hover {
    background-color: rgba(255, 255, 255, 0.05);
}

/* Modal enhancements */
.modal-content {
    border: none;
    border-radius: 0.5rem;
}

.modal-header {
    border-bottom: 1px solid var(--bs-border-color);
}

.modal-footer {
    border-top: 1px solid var(--bs-border-color);
}

/* Empty state styling */
.text-center.py-5 {
    padding: 3rem 1rem !important;
}

.text-center.py-5 i {
    opacity: 0.5;
}

/* Tooltip enhancements */
.tooltip {
    font-size: 0.875rem;
}

.tooltip-inner {
    background-color: rgba(0, 0, 0, 0.9);
    border-radius: 0.375rem;
}

/* Focus improvements for accessibility */
.btn:focus,
.form-control:focus,
.form-check-input:focus {
    outline: none;
}

.btn:focus-visible,
.form-control:focus-visible,
.form-check-input:focus-visible {
    outline: 2px solid hsl(var(--primary-color));
    outline-offset: 2px;
}

/* Print styles */
@media print {
    .navbar,
    .btn,
    .modal {
        display: none !important;
    }
    
    .card {
        border: 1px solid #000 !important;
        box-shadow: none !important;
    }
    
    .container {
        max-width: none !important;
    }
}
