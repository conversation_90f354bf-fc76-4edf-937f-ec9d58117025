# 🤖 AI Team Orchestrator - Multi-Agent Complaint Management System

Un système de gestion automatisée des réclamations pour concours de recrutement bancaire, orchestré par une équipe d'agents IA coopérants utilisant LangGraph.

## 🎯 Objectif

Créer une application web capable de traiter automatiquement toutes les réclamations reçues par e-mail pour un concours de recrutement, en utilisant une architecture multi-agents avec LangGraph pour une meilleure coordination et gestion d'état.

## 🏗️ Architecture

### Agents IA Principaux

1. **AgentMailReader** - Lecture et analyse des e-mails
2. **AgentClassifier** - Classification automatique des types de réclamations
3. **AgentDatabaseHandler** - Gestion des opérations base de données
4. **AgentResponseGenerator** - Génération de réponses personnalisées
5. **AgentRHSupervisor** - Supervision et validation RH
6. **AgentLogger** - Journalisation et traçabilité

### Technologies Utilisées

- **Backend**: Python Flask
- **Orchestration**: LangGraph + <PERSON><PERSON>hain
- **IA**: Groq API (Llama 3.3 70B)
- **Base de données**: MySQL/Oracle (abstraction)
- **Email**: IMAP/SMTP
- **Frontend**: Bootstrap 5 + JavaScript
- **Monitoring**: Système de surveillance automatique

## 🚀 Fonctionnalités

### ✅ Fonctionnalités Implémentées

- **Traitement Multi-Agent**: Orchestration LangGraph pour coordination optimale
- **Classification IA**: Détection automatique des types de réclamations
- **Base de Données Flexible**: Support MySQL et Oracle
- **Interface Web**: Dashboard RH complet et responsive
- **Surveillance Email**: Monitoring automatique des boîtes mail
- **Réponses Automatiques**: Génération et envoi de réponses personnalisées
- **Validation RH**: Workflow d'approbation pour actions sensibles
- **Traçabilité**: Logs détaillés de toutes les opérations

### 📧 Types de Réclamations Gérées

1. **CIN déjà utilisé** - Suppression automatique pour réinscription
2. **Modification d'informations** - Mise à jour des données candidat
3. **Problème d'impression** - Guide technique automatique
4. **Liens cassés** - Support technique et alternatives
5. **Email non reçu** - Vérification et renvoi
6. **Erreur de fichier** - Assistance upload de documents
7. **Questions processus** - Réponses automatiques sur les règles
8. **Autres** - Classification et traitement adaptatif

## 🛠️ Installation

### Prérequis

- Python 3.11+
- MySQL ou Oracle Database
- Compte email IMAP/SMTP
- Clé API Groq

### Configuration

1. **Cloner le projet**
```bash
git clone <repository-url>
cd AITeamOrchestrator
```

2. **Installer les dépendances**
```bash
pip install -e .
```

3. **Configuration environnement**
```bash
cp .env.example .env
# Éditer .env avec vos paramètres
```

4. **Variables d'environnement essentielles**
```env
# Base de données
DATABASE_TYPE=mysql
MYSQL_HOST=localhost
MYSQL_DATABASE=hr_complaints
MYSQL_USER=root
MYSQL_PASSWORD=your_password

# IA
GROQ_API_KEY=your_groq_api_key

# Email
EMAIL_HOST=imap.gmail.com
EMAIL_USER=<EMAIL>
EMAIL_PASSWORD=your_password
SMTP_HOST=smtp.gmail.com
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your_smtp_password
```

### Démarrage

```bash
python main.py
```

L'application sera accessible sur `http://localhost:5000`

## 📊 Utilisation

### Dashboard Principal

- **Vue d'ensemble**: Statistiques des réclamations
- **Soumission manuelle**: Test du système
- **Liste des réclamations**: Suivi en temps réel
- **Contrôles**: Validation/rejet des actions

### Monitoring Email

- **Surveillance automatique**: Vérification périodique des emails
- **Classification intelligente**: Détection automatique des réclamations
- **Traitement en lot**: Gestion efficace des volumes
- **Réponses automatiques**: Envoi immédiat pour cas simples

### Workflow LangGraph

1. **Lecture Email** → **Classification** → **Détermination Actions**
2. **Génération Réponse** → **Vérification Validation**
3. **Exécution/Validation** → **Sauvegarde** → **Notification**

## 🔧 Configuration Avancée

### Base de Données

Le système supporte MySQL et Oracle via une couche d'abstraction:

```python
# MySQL
DATABASE_TYPE=mysql
MYSQL_HOST=localhost
MYSQL_PORT=3306

# Oracle
DATABASE_TYPE=oracle
ORACLE_HOST=localhost
ORACLE_PORT=1521
ORACLE_SERVICE_NAME=XE
```

### Surveillance Email

```env
EMAIL_CHECK_INTERVAL=300  # secondes
MAX_EMAILS_PER_BATCH=10
EMAIL_ARCHIVE_FOLDER=Processed
AUTO_EXECUTE_SAFE_ACTIONS=true
```

### Seuils de Validation

```env
REQUIRE_HR_VALIDATION_THRESHOLD=0.7
HIGH_URGENCY_NOTIFICATION=true
```

## 🧪 Tests

### Test Manuel

1. Accéder au dashboard
2. Soumettre une réclamation test
3. Vérifier la classification et les actions proposées
4. Valider ou rejeter selon le cas

### Test Email

1. Envoyer un email à l'adresse configurée
2. Vérifier la détection automatique
3. Contrôler le traitement dans le dashboard

### Exemples de Réclamations

```text
Bonjour,
J'ai un problème avec mon inscription au concours. Mon CIN AB123456 est déjà utilisé mais je n'ai jamais fait d'inscription. Pouvez-vous m'aider ?
Cordialement,
Ahmed Ben Ali
```

## 📈 Monitoring et Logs

### Logs Système

- **Niveau**: INFO, WARNING, ERROR
- **Fichier**: `logs/app.log`
- **Rotation**: Automatique

### Métriques

- Nombre de réclamations traitées
- Temps de traitement moyen
- Taux de validation automatique
- Précision de classification

## 🔒 Sécurité

- **Validation d'entrée**: Sanitisation des données
- **Authentification**: Sessions sécurisées
- **Base de données**: Requêtes préparées
- **Email**: Connexions chiffrées (SSL/TLS)

## 🚀 Déploiement

### Production

1. **Variables d'environnement**
```bash
FLASK_DEBUG=false
FLASK_SECRET_KEY=your-production-secret
```

2. **Base de données**
```bash
# Créer la base de données
# Configurer les utilisateurs et permissions
```

3. **Serveur web**
```bash
gunicorn -w 4 -b 0.0.0.0:5000 main:app
```

### Docker (Optionnel)

```dockerfile
FROM python:3.11-slim
WORKDIR /app
COPY . .
RUN pip install -e .
CMD ["gunicorn", "-w", "4", "-b", "0.0.0.0:5000", "main:app"]
```

## 🤝 Contribution

1. Fork le projet
2. Créer une branche feature
3. Commit les changements
4. Push vers la branche
5. Ouvrir une Pull Request

## 📝 Licence

Ce projet est sous licence MIT. Voir le fichier `LICENSE` pour plus de détails.

## 🆘 Support

Pour toute question ou problème:

1. Vérifier les logs dans `logs/app.log`
2. Consulter la documentation des APIs
3. Tester avec des données d'exemple
4. Contacter l'équipe de développement

---

**Développé avec ❤️ pour l'automatisation RH**
