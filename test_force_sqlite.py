#!/usr/bin/env python3
"""
Force SQLite test
"""

import os
import sys

# Force SQLite before importing anything else
os.environ['DATABASE_TYPE'] = 'sqlite'

from dotenv import load_dotenv
load_dotenv()

def test_forced_sqlite():
    """Test with forced SQLite"""
    print("🔍 Forced SQLite Test")
    print("=" * 30)
    
    # Check environment variable
    db_type = os.getenv('DATABASE_TYPE', 'sqlite')
    print(f"DATABASE_TYPE: {db_type}")
    
    try:
        # Import database module AFTER setting environment
        from database import db_manager
        
        print(f"✅ Database module imported")
        print(f"   Database type: {db_manager.db_type}")
        
        # Test connection
        conn = db_manager.get_connection()
        print("✅ Database connection successful")
        
        # Test candidates
        candidates = db_manager.get_all_candidates()
        print(f"✅ Found {len(candidates)} candidates")
        
        print("🎉 Forced SQLite test successful!")
        return True
        
    except Exception as e:
        print(f"❌ Forced SQLite test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_forced_sqlite()
    sys.exit(0 if success else 1)
