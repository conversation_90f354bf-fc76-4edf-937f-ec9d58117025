🎯 Objectif global
Créer une application web orchestrée par une équipe d’agents IA (multi-agents) capables de traiter automatiquement toutes les réclamations reçues par e-mail pour un concours de recrutement. Cette application utilisera une base de données MySQL (avec possibilité de basculer vers Oracle plus tard) et sera déployée gratuitement avec LangGraph + Augment AI dans Visual Studio Code.

🧠 Prompt complet pour Augment AI
Tu es un assistant IA chargé de concevoir une application web de gestion automatisée des réclamations pour un concours de recrutement bancaire. Cette application est construite comme une équipe d’agents IA coopérants.

1. Rôles principaux de l’application
Lire et comprendre les e-mails entrants envoyés à la Direction RH.

Détecter et classifier automatiquement les différents types de réclamations exprimées par les candidats.

Déclencher des actions automatisées pour résoudre chaque problème identifié : suppression d’une ligne dans la base de données, envoi de mail de réponse, redirection vers un guide, etc.

Laisser la possibilité à la RH de valider ou corriger l'action avant exécution si besoin.

2. Source des réclamations
Les réclamations proviennent d’un compte mail professionnel configuré pour centraliser les messages des candidats. Chaque message contient un ou plusieurs problèmes, souvent sous forme libre (langage naturel).

3. Exemples de problèmes à comprendre par les agents :
CIN déjà utilisé : Le candidat souhaite refaire son inscription mais le système le bloque car le CIN est déjà enregistré.

Modification des informations : Le candidat a fait une erreur sur son nom, prénom ou autre info et veut la corriger.

Bouton imprimer ne fonctionne pas : Le candidat ne parvient pas à imprimer sa fiche, souvent à cause d’un outil PDF.

Lien ou bouton cassé : Problème technique lors de la navigation.

Non réception d’e-mail de confirmation.

Erreur de pièce jointe ou document non chargé.

Incompréhension des étapes ou règles du concours.

Cas non prévu : L’agent IA doit être capable d’analyser et créer une nouvelle catégorie sémantiquement.

4. Architecture fonctionnelle attendue
Application web responsive (pouvant être utilisée par la RH).

Affichage en temps réel des réclamations classées automatiquement.

Interface de validation pour la RH avec actions suggérées par les agents IA.

Moteur IA capable d’extraire des informations clés : CIN, nom, email, type de problème, degré d’urgence.

Connexion à une base MySQL (avec flexibilité pour Oracle).

Actions comme :

Suppression de l’utilisateur (si CIN dupliqué),

Envoi automatique de mails avec explication ou lien,

Génération de réponse personnalisée à chaque type de demande,

Notification RH en cas de doute ou demande sensible.

5. Agents à implémenter
AgentMailReader : lit les e-mails automatiquement et les transmet aux agents.

AgentClassifier : comprend le contenu du message et classe le problème.

AgentDatabaseHandler : interagit avec la base MySQL pour supprimer, rechercher, ou modifier une ligne.

AgentResponseGenerator : crée une réponse personnalisée selon le type de problème.

AgentRHSupervisor : propose à la RH une validation ou modération avant envoi ou action sensible.

6. Contraintes techniques
Tout doit fonctionner avec des outils open source ou gratuits.

L’ensemble doit être compatible avec LangGraph et exécuté dans VS Code ou Replit.

La base de données cible est MySQL, mais doit pouvoir facilement passer à Oracle via abstraction.

L’interface doit être claire, simple, accessible à un utilisateur RH non technique.

7. Fonctionnalités optionnelles (à ajouter après MVP)
Dashboard analytique pour visualiser les types de réclamations les plus fréquents.

Suivi des temps de traitement.

Moteur d’apprentissage (fine-tuning possible) basé sur les validations de la RH.

8. Résultat attendu de ta mission
Générer une application complète multi-agents avec LangGraph dans Augment AI, capable de :

Lire les réclamations depuis des mails (ou fichiers .eml / .txt / webhook),

Comprendre et classer chaque problème,

Exécuter les actions correspondantes ou proposer une réponse à la RH,

Suivre l’évolution de chaque cas dans une interface web simple,

S’adapter à de nouveaux problèmes sans code supplémentaire.

Tu peux utiliser des frameworks Python gratuits, intégrer une base MySQL locale et structurer les réponses des agents sous forme JSON ou YAML.
L’interface peut être développée avec Flask, Streamlit ou FastAPI + React (au choix selon simplicité).
Tu dois proposer une architecture modulaire, légère et maintenable.

🚀 Ce que tu dois faire maintenant dans Augment AI :
Colle ce prompt dans l’espace de conception de ton projet dans VS Code via l'extension Augment AI.

Utilise le moteur de génération de code guidé pour :

Créer les agents un par un.

Définir leurs interactions dans le graphe LangGraph.

Déployer ton interface RH en front (Streamlit est très rapide à déployer).

Configure ta base de données MySQL et connecte-la à l’agent AgentDatabaseHandler.

Souhaites-tu maintenant que je te génère :

le diagramme complet des agents (visuel ou texte),

ou le modèle de base de données MySQL à utiliser ?

Je peux aussi te donner des réponses-types automatiques que l’agent peut envoyer selon la catégorie.






Vous avez dit :
le diagramme complet des agents (visuel ou texte),


ChatGPT a dit :
Voici le diagramme complet des agents IA pour ton projet, au format texte explicatif structuré, suivi d’une représentation visuelle simplifiée (type organigramme) que tu peux intégrer dans Augment AI avec LangGraph.

🧠 Diagramme complet des agents IA – version texte
🔁 1. AgentMailReader
Rôle : Scanner les e-mails reçus dans la boîte RH.

Entrée : Mail brut (texte, .eml ou webhook).

Sortie : Contenu extrait + metadata (objet, expéditeur, corps du message).

Action : Appelle AgentClassifier.

🔍 2. AgentClassifier
Rôle : Identifier le(s) type(s) de problème(s) mentionnés dans l’email.

Entrée : Texte du mail.

Sortie : Catégories détectées + extrait pertinent + niveau d’urgence.

Catégories possibles :

CIN déjà utilisé

Modification infos

Bouton imprimer / erreur navigateur

Problème lien / fichier

Problème technique divers

Questions sur le concours

Non classifié (demande exceptionnelle)

Action :

Appelle AgentDatabaseHandler pour CIN / erreurs de saisie.

Appelle AgentResponseGenerator pour réponse simple.

Appelle AgentRHSupervisor si doute.

🧩 3. AgentDatabaseHandler
Rôle : Exécuter les actions sur la base de données (MySQL / Oracle).

Entrée : Instruction d'action + données (CIN, Email, etc.).

Sortie : Résultat de la requête (succès/échec + logs).

Actions possibles :

Supprimer une ligne via CIN

Chercher une ligne (diagnostic)

Mettre à jour une information

Appelé par : AgentClassifier, validé éventuellement par AgentRHSupervisor.

💬 4. AgentResponseGenerator
Rôle : Générer une réponse automatique et personnalisée selon la catégorie.

Entrée : Type de demande + informations extraites.

Sortie : Texte prêt à envoyer.

Exemples de réponses :

Pour bouton imprimer : "Merci pour votre message. Veuillez utiliser un navigateur web (Chrome ou Firefox) pour imprimer votre fiche de candidature, et non un lecteur PDF."

Pour CIN : "Votre ancien enregistrement a été supprimé. Vous pouvez vous réinscrire via le lien suivant…"

✅ 5. AgentRHSupervisor
Rôle : Interface intermédiaire avec la RH pour valider certaines actions ou cas douteux.

Entrée : Cas marqué comme ambigu / critique par d'autres agents.

Sortie : Action validée ou modifiée manuellement.

Notifications :

Email automatique à RH

Affichage dans une interface simple avec bouton “Valider / Refuser / Modifier”

📊 6. AgentLogger (optionnel mais recommandé)
Rôle : Journaliser toutes les actions exécutées.

Entrée : Toute action ou message traité.

Sortie : Log structuré (stocké en BDD ou fichier).

Permet :

Traçabilité

Audit des actions

Retour analytique (dashboards futurs)

📈 Diagramme visuel simplifié (type organigramme)
csharp
Copier
Modifier
     [AgentMailReader]
            |
            v
     [AgentClassifier]
       /    |     \
      /     |      \
[AgentDatabase]  [AgentResponse] <-+
      \         /        |        |
       \       /         |        |
       [AgentRHSupervisor] <------+
                |
                v
           (Validation RH)

               |
               v
         [AgentLogger]