<!DOCTYPE html>
<html lang="en" data-bs-theme="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Complaint {{ complaint.id }} - AI Management System</title>
    <link href="https://cdn.replit.com/agent/bootstrap-agent-dark-theme.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/custom.css') }}" rel="stylesheet">
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="{{ url_for('index') }}">
                <i class="fas fa-robot me-2"></i>
                AI Complaint Management
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="{{ url_for('index') }}">
                    <i class="fas fa-arrow-left me-1"></i>Back to Dashboard
                </a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- Flash Messages -->
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'danger' if category == 'error' else 'success' if category == 'success' else 'info' }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        <!-- Complaint Header -->
        <div class="card mb-4">
            <div class="card-header">
                <div class="row align-items-center">
                    <div class="col">
                        <h4><i class="fas fa-file-alt me-2"></i>Complaint {{ complaint.id }}</h4>
                        <p class="mb-0 text-muted">Processed by multi-agent AI system</p>
                    </div>
                    <div class="col-auto">
                        {% set status = complaint.status %}
                        <span class="badge bg-{{ 'warning' if status == 'pending_validation' else 'success' if status in ['auto_resolved', 'hr_validated'] else 'secondary' }} fs-6">
                            {{ status.replace('_', ' ').title() }}
                        </span>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <strong>Created:</strong> {{ complaint.created_at.split('T')[0] }} at {{ complaint.created_at.split('T')[1].split('.')[0] }}<br>
                        <strong>Sender:</strong> {{ complaint.email_data.sender_email }}<br>
                        <strong>Subject:</strong> {{ complaint.email_data.subject or 'No subject' }}
                    </div>
                    <div class="col-md-6">
                        {% set classification = complaint.classification %}
                        <strong>Category:</strong> <span class="badge bg-secondary">{{ classification.category.replace('_', ' ').title() }}</span><br>
                        <strong>Urgency:</strong> <span class="badge bg-{{ 'danger' if classification.urgency == 'high' else 'warning' if classification.urgency == 'medium' else 'success' }}">{{ classification.urgency.title() }}</span><br>
                        <strong>AI Confidence:</strong> {{ (classification.confidence * 100)|round }}%
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- Original Email -->
            <div class="col-lg-6 mb-4">
                <div class="card h-100">
                    <div class="card-header">
                        <h5><i class="fas fa-envelope me-2"></i>Original Email</h5>
                    </div>
                    <div class="card-body">
                        <div class="email-content p-3 bg-dark rounded">
                            {{ complaint.email_data.content|replace('\n', '<br>')|safe }}
                        </div>
                        
                        {% if complaint.classification.extracted_info %}
                        <div class="mt-3">
                            <h6>Extracted Information:</h6>
                            <ul class="list-unstyled">
                                {% for key, value in complaint.classification.extracted_info.items() %}
                                    {% if value %}
                                    <li><strong>{{ key.replace('_', ' ').title() }}:</strong> {{ value }}</li>
                                    {% endif %}
                                {% endfor %}
                            </ul>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- AI Analysis -->
            <div class="col-lg-6 mb-4">
                <div class="card h-100">
                    <div class="card-header">
                        <h5><i class="fas fa-brain me-2"></i>AI Classification</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label class="form-label">Category:</label>
                            <span class="badge bg-secondary ms-2">{{ complaint.classification.category.replace('_', ' ').title() }}</span>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">Confidence Level:</label>
                            <div class="progress mt-1">
                                {% set confidence = complaint.classification.confidence %}
                                <div class="progress-bar bg-{{ 'success' if confidence > 0.8 else 'warning' if confidence > 0.5 else 'danger' }}" 
                                     style="width: {{ (confidence * 100)|round }}%">{{ (confidence * 100)|round }}%</div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">AI Reasoning:</label>
                            <p class="text-muted">{{ complaint.classification.reasoning }}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Proposed Actions -->
        {% if complaint.proposed_actions %}
        <div class="card mb-4">
            <div class="card-header">
                <h5><i class="fas fa-cogs me-2"></i>Proposed Actions</h5>
            </div>
            <div class="card-body">
                {% if complaint.status == 'pending_validation' %}
                    <form method="POST" action="{{ url_for('validate_complaint') }}">
                        <input type="hidden" name="complaint_id" value="{{ complaint.id }}">
                        
                        {% for action in complaint.proposed_actions %}
                        {% set i = loop.index0 %}
                        <div class="form-check mb-3 p-3 border rounded">
                            <input class="form-check-input" type="checkbox" name="approved_actions" value="{{ i }}" id="action{{ i }}">
                            <label class="form-check-label" for="action{{ i }}">
                                <strong>{{ action.description }}</strong><br>
                                <small class="text-muted">Type: {{ action.type }}</small>
                                {% if action.data %}
                                <br><small class="text-muted">Data: {{ action.data }}</small>
                                {% endif %}
                            </label>
                        </div>
                        {% endfor %}
                        
                        <div class="mb-3">
                            <label for="hr_comments" class="form-label">HR Comments:</label>
                            <textarea class="form-control" id="hr_comments" name="hr_comments" rows="3" 
                                      placeholder="Add any comments or notes..."></textarea>
                        </div>
                        
                        <div class="btn-group">
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-check me-2"></i>Approve Selected Actions
                            </button>
                            <button type="button" class="btn btn-danger" data-bs-toggle="modal" data-bs-target="#rejectModal">
                                <i class="fas fa-times me-2"></i>Reject Complaint
                            </button>
                        </div>
                    </form>
                {% else %}
                    {% for action in complaint.proposed_actions %}
                    <div class="mb-3 p-3 border rounded">
                        <strong>{{ action.description }}</strong><br>
                        <small class="text-muted">Type: {{ action.type }}</small>
                        {% if action.data %}
                        <br><small class="text-muted">Data: {{ action.data }}</small>
                        {% endif %}
                    </div>
                    {% endfor %}
                {% endif %}
            </div>
        </div>
        {% endif %}

        <!-- Suggested Response -->
        {% if complaint.suggested_response %}
        <div class="card mb-4">
            <div class="card-header">
                <h5><i class="fas fa-reply me-2"></i>Suggested Response</h5>
            </div>
            <div class="card-body">
                <div class="response-content p-3 bg-dark rounded">
                    {{ complaint.suggested_response.content|replace('\n', '<br>')|safe }}
                </div>
                <div class="mt-3">
                    <small class="text-muted">
                        Generated at: {{ complaint.suggested_response.generated_at }}<br>
                        Personalized: {{ 'Yes' if complaint.suggested_response.personalized else 'No' }}<br>
                        Requires Review: {{ 'Yes' if complaint.suggested_response.requires_review else 'No' }}
                    </small>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- Action Results -->
        {% if complaint.action_results %}
        <div class="card mb-4">
            <div class="card-header">
                <h5><i class="fas fa-history me-2"></i>Execution Results</h5>
            </div>
            <div class="card-body">
                {% for result in complaint.action_results %}
                <div class="mb-3 p-3 border rounded">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-{{ 'check-circle text-success' if result.success else 'exclamation-circle text-danger' }} me-2"></i>
                        <strong>{{ 'Success' if result.success else 'Failed' }}</strong>
                    </div>
                    <p class="mb-1">{{ result.message }}</p>
                    {% if result.data %}
                    <small class="text-muted">Data: {{ result.data }}</small>
                    {% endif %}
                </div>
                {% endfor %}
            </div>
        </div>
        {% endif %}

        <!-- HR Comments -->
        {% if complaint.hr_comments %}
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-user-tie me-2"></i>HR Comments</h5>
            </div>
            <div class="card-body">
                <p>{{ complaint.hr_comments }}</p>
            </div>
        </div>
        {% endif %}
    </div>

    <!-- Reject Modal -->
    <div class="modal fade" id="rejectModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <form method="POST" action="{{ url_for('reject_complaint', complaint_id=complaint.id) }}">
                    <div class="modal-header">
                        <h5 class="modal-title">Reject Complaint</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="mb-3">
                            <label for="rejection_reason" class="form-label">Reason for Rejection:</label>
                            <textarea class="form-control" id="rejection_reason" name="rejection_reason" rows="3" required
                                      placeholder="Please provide a reason for rejecting this complaint..."></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-danger">Reject Complaint</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="{{ url_for('static', filename='js/main.js') }}"></script>
</body>
</html>
