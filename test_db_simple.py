#!/usr/bin/env python3
"""
Simple database test
"""

import os
import sqlite3
from dotenv import load_dotenv

load_dotenv()

def test_simple():
    """Test simple SQLite connection"""
    print("🔍 Simple SQLite Test")
    print("=" * 30)
    
    # Check environment variable
    db_type = os.getenv('DATABASE_TYPE', 'sqlite')
    print(f"DATABASE_TYPE from .env: {db_type}")
    
    # Test direct SQLite connection
    try:
        db_path = os.getenv('SQLITE_DATABASE', 'hr_complaints.db')
        print(f"Database path: {db_path}")
        
        conn = sqlite3.connect(db_path)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        # Create test table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS test_table (
                id INTEGER PRIMARY KEY,
                name TEXT NOT NULL
            )
        """)
        
        # Insert test data
        cursor.execute("INSERT INTO test_table (name) VALUES (?)", ("Test Entry",))
        conn.commit()
        
        # Read test data
        cursor.execute("SELECT * FROM test_table")
        results = cursor.fetchall()
        
        print(f"✅ SQLite working - {len(results)} records found")
        
        # Clean up
        cursor.execute("DROP TABLE test_table")
        conn.commit()
        cursor.close()
        conn.close()
        
        print("✅ Simple SQLite test successful!")
        return True
        
    except Exception as e:
        print(f"❌ Simple SQLite test failed: {e}")
        return False

if __name__ == "__main__":
    test_simple()
