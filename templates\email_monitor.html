<!DOCTYPE html>
<html lang="en" data-bs-theme="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Email Monitor - AI Complaint Management</title>
    <link href="https://cdn.replit.com/agent/bootstrap-agent-dark-theme.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/custom.css') }}" rel="stylesheet">
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="{{ url_for('index') }}">
                <i class="fas fa-robot me-2"></i>
                AI Complaint Management
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="{{ url_for('index') }}">
                    <i class="fas fa-home me-1"></i>Dashboard
                </a>
                <span class="navbar-text">
                    Email Monitor
                </span>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- Flash Messages -->
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'danger' if category == 'error' else 'success' if category == 'success' else 'info' }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        <!-- Email Monitor Status -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5><i class="fas fa-envelope-open-text me-2"></i>Email Monitor Status</h5>
                        <div>
                            {% if status.is_running %}
                                <span class="badge bg-success">
                                    <i class="fas fa-play me-1"></i>Running
                                </span>
                            {% else %}
                                <span class="badge bg-secondary">
                                    <i class="fas fa-stop me-1"></i>Stopped
                                </span>
                            {% endif %}
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="text-center">
                                    <i class="fas fa-clock fa-2x text-primary mb-2"></i>
                                    <h6>Check Interval</h6>
                                    <p class="mb-0">{{ status.check_interval }} seconds</p>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center">
                                    <i class="fas fa-robot fa-2x text-info mb-2"></i>
                                    <h6>Auto Execute</h6>
                                    <p class="mb-0">
                                        {% if status.auto_execute %}
                                            <span class="text-success">Enabled</span>
                                        {% else %}
                                            <span class="text-warning">Disabled</span>
                                        {% endif %}
                                    </p>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center">
                                    <i class="fas fa-calendar-alt fa-2x text-warning mb-2"></i>
                                    <h6>Next Check</h6>
                                    <p class="mb-0">
                                        {% if status.next_check %}
                                            {{ status.next_check.strftime('%H:%M:%S') if status.next_check else 'N/A' }}
                                        {% else %}
                                            N/A
                                        {% endif %}
                                    </p>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center">
                                    <i class="fas fa-cog fa-2x text-secondary mb-2"></i>
                                    <h6>Status</h6>
                                    <p class="mb-0">
                                        {% if status.is_running %}
                                            <span class="text-success">Active</span>
                                        {% else %}
                                            <span class="text-muted">Inactive</span>
                                        {% endif %}
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Control Panel -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-sliders-h me-2"></i>Control Panel</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                {% if status.is_running %}
                                    <form method="POST" action="{{ url_for('stop_email_monitor') }}" class="d-inline">
                                        <button type="submit" class="btn btn-danger w-100">
                                            <i class="fas fa-stop me-2"></i>Stop Monitoring
                                        </button>
                                    </form>
                                {% else %}
                                    <form method="POST" action="{{ url_for('start_email_monitor') }}" class="d-inline">
                                        <button type="submit" class="btn btn-success w-100">
                                            <i class="fas fa-play me-2"></i>Start Monitoring
                                        </button>
                                    </form>
                                {% endif %}
                            </div>
                            <div class="col-md-4 mb-3">
                                <form method="POST" action="{{ url_for('check_emails_now') }}" class="d-inline">
                                    <button type="submit" class="btn btn-primary w-100">
                                        <i class="fas fa-sync-alt me-2"></i>Check Now
                                    </button>
                                </form>
                            </div>
                            <div class="col-md-4 mb-3">
                                <button type="button" class="btn btn-info w-100" onclick="refreshStatus()">
                                    <i class="fas fa-refresh me-2"></i>Refresh Status
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Email Configuration Info -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-cogs me-2"></i>Configuration</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6><i class="fas fa-download me-2"></i>IMAP Settings</h6>
                                <ul class="list-unstyled">
                                    <li><strong>Host:</strong> <code>{{ config.EMAIL_HOST or 'Not configured' }}</code></li>
                                    <li><strong>Port:</strong> <code>{{ config.EMAIL_PORT or 'Not configured' }}</code></li>
                                    <li><strong>SSL:</strong> 
                                        {% if config.EMAIL_USE_SSL %}
                                            <span class="text-success">Enabled</span>
                                        {% else %}
                                            <span class="text-warning">Disabled</span>
                                        {% endif %}
                                    </li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h6><i class="fas fa-upload me-2"></i>SMTP Settings</h6>
                                <ul class="list-unstyled">
                                    <li><strong>Host:</strong> <code>{{ config.SMTP_HOST or 'Not configured' }}</code></li>
                                    <li><strong>Port:</strong> <code>{{ config.SMTP_PORT or 'Not configured' }}</code></li>
                                    <li><strong>TLS:</strong> 
                                        {% if config.SMTP_USE_TLS %}
                                            <span class="text-success">Enabled</span>
                                        {% else %}
                                            <span class="text-warning">Disabled</span>
                                        {% endif %}
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Activity Log -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5><i class="fas fa-history me-2"></i>Activity Log</h5>
                        <small class="text-muted">Last updated: <span id="lastUpdate">{{ moment().format('HH:mm:ss') }}</span></small>
                    </div>
                    <div class="card-body">
                        <div id="activityLog" style="max-height: 400px; overflow-y: auto;">
                            <div class="text-center text-muted py-3">
                                <i class="fas fa-clock fa-2x mb-2"></i>
                                <p>Activity log will appear here when monitoring is active</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function refreshStatus() {
            fetch('/api/email_monitor/status')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        location.reload();
                    } else {
                        console.error('Failed to refresh status:', data.error);
                    }
                })
                .catch(error => {
                    console.error('Error refreshing status:', error);
                });
        }

        // Auto-refresh status every 30 seconds
        setInterval(refreshStatus, 30000);

        // Update last update time
        function updateLastUpdateTime() {
            document.getElementById('lastUpdate').textContent = new Date().toLocaleTimeString();
        }

        setInterval(updateLastUpdateTime, 1000);
    </script>
</body>
</html>
