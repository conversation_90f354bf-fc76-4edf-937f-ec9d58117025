#!/usr/bin/env python3
"""
Quick start script for AI Team Orchestrator
Minimal dependencies version for testing
"""

import os
import sys
import subprocess

def check_and_install_minimal_deps():
    """Check and install only the most essential dependencies"""
    
    minimal_deps = [
        "flask",
        "python-dotenv", 
        "groq",
        "mysql-connector-python",
    ]
    
    print("🔍 Checking minimal dependencies...")
    
    missing_deps = []
    
    for dep in minimal_deps:
        try:
            __import__(dep.replace('-', '_'))
            print(f"✅ {dep} - OK")
        except ImportError:
            print(f"❌ {dep} - Missing")
            missing_deps.append(dep)
    
    if missing_deps:
        print(f"\n📦 Installing missing dependencies: {', '.join(missing_deps)}")
        for dep in missing_deps:
            try:
                subprocess.check_call([sys.executable, "-m", "pip", "install", dep])
                print(f"✅ {dep} installed")
            except subprocess.CalledProcessError:
                print(f"❌ Failed to install {dep}")
                return False
    
    return True

def create_minimal_env():
    """Create a minimal .env file if it doesn't exist"""
    if not os.path.exists('.env'):
        print("📝 Creating minimal .env file...")
        
        env_content = """# AI Team Orchestrator - Minimal Configuration
DATABASE_TYPE=mysql
MYSQL_HOST=localhost
MYSQL_PORT=3306
MYSQL_DATABASE=hr_complaints
MYSQL_USER=root
MYSQL_PASSWORD=

# AI Configuration - REQUIRED
GROQ_API_KEY=********************************************************

# Flask Configuration
FLASK_SECRET_KEY=dev-secret-key
FLASK_DEBUG=true
FLASK_HOST=0.0.0.0
FLASK_PORT=5000
"""
        
        with open('.env', 'w') as f:
            f.write(env_content)
        
        print("✅ .env file created")
        print("💡 Edit .env to configure your database settings")

def start_minimal_app():
    """Start the application with minimal features"""
    
    print("🚀 Starting AI Team Orchestrator (Minimal Mode)...")
    
    # Create logs directory
    if not os.path.exists('logs'):
        os.makedirs('logs')
    
    try:
        # Import and start the basic Flask app
        from app import app
        
        print("✅ Flask app loaded")
        print("🌐 Starting server...")
        print("📊 Dashboard: http://localhost:5000")
        print("🛑 Press Ctrl+C to stop")
        
        app.run(
            host='0.0.0.0',
            port=5000,
            debug=True
        )
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("💡 Some dependencies might be missing")
        return False
    except Exception as e:
        print(f"❌ Startup error: {e}")
        return False
    
    return True

def main():
    """Main quick start process"""
    print("⚡ AI Team Orchestrator - Quick Start")
    print("=" * 40)
    
    # Check Python version
    if sys.version_info < (3, 8):
        print("❌ Python 3.8+ required")
        return False
    
    # Check and install minimal dependencies
    if not check_and_install_minimal_deps():
        print("❌ Failed to install dependencies")
        return False
    
    # Create minimal configuration
    create_minimal_env()
    
    # Start the application
    return start_minimal_app()

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n🛑 Shutting down...")
        print("👋 Goodbye!")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        sys.exit(1)
