"""
Data models for the complaint management system.
Since we're using in-memory storage, these are simple dataclasses
that define the structure of our data.
"""

from datetime import datetime
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, field

@dataclass
class Candidate:
    """Represents a recruitment candidate"""
    cin: str
    name: str
    email: str
    status: str = "registered"
    created_at: str = field(default_factory=lambda: datetime.now().isoformat())
    
    def to_dict(self) -> Dict:
        return {
            "cin": self.cin,
            "name": self.name,
            "email": self.email,
            "status": self.status,
            "created_at": self.created_at
        }

@dataclass
class EmailData:
    """Represents processed email data"""
    content: str
    sender_email: str
    subject: str
    processed_at: str = field(default_factory=lambda: datetime.now().isoformat())
    extracted_cin: Optional[str] = None
    
    def to_dict(self) -> Dict:
        return {
            "content": self.content,
            "sender_email": self.sender_email,
            "subject": self.subject,
            "processed_at": self.processed_at,
            "extracted_cin": self.extracted_cin
        }

@dataclass
class Classification:
    """Represents AI classification of a complaint"""
    category: str
    urgency: str  # low, medium, high
    confidence: float  # 0.0 to 1.0
    reasoning: str
    extracted_info: Dict = field(default_factory=dict)
    classified_at: str = field(default_factory=lambda: datetime.now().isoformat())
    
    def to_dict(self) -> Dict:
        return {
            "category": self.category,
            "urgency": self.urgency,
            "confidence": self.confidence,
            "reasoning": self.reasoning,
            "extracted_info": self.extracted_info,
            "classified_at": self.classified_at
        }

@dataclass
class Action:
    """Represents an action to be taken"""
    type: str
    data: Dict
    description: str
    status: str = "pending"  # pending, executed, failed
    
    def to_dict(self) -> Dict:
        return {
            "type": self.type,
            "data": self.data,
            "description": self.description,
            "status": self.status
        }

@dataclass
class Response:
    """Represents a generated response"""
    content: str
    category: str
    personalized: bool = False
    requires_review: bool = False
    generated_at: str = field(default_factory=lambda: datetime.now().isoformat())
    
    def to_dict(self) -> Dict:
        return {
            "content": self.content,
            "category": self.category,
            "personalized": self.personalized,
            "requires_review": self.requires_review,
            "generated_at": self.generated_at
        }

@dataclass
class ValidationRequest:
    """Represents an HR validation request"""
    complaint_id: str
    status: str = "pending_validation"
    validation_reasons: List[str] = field(default_factory=list)
    created_at: str = field(default_factory=lambda: datetime.now().isoformat())
    validated_at: Optional[str] = None
    hr_comments: Optional[str] = None
    
    def to_dict(self) -> Dict:
        return {
            "complaint_id": self.complaint_id,
            "status": self.status,
            "validation_reasons": self.validation_reasons,
            "created_at": self.created_at,
            "validated_at": self.validated_at,
            "hr_comments": self.hr_comments
        }

@dataclass
class Complaint:
    """Represents a complete complaint record"""
    id: str
    email_data: Dict
    classification: Dict
    proposed_actions: List[Dict] = field(default_factory=list)
    suggested_response: Dict = field(default_factory=dict)
    status: str = "pending"
    created_at: str = field(default_factory=lambda: datetime.now().isoformat())
    validation_request: Optional[Dict] = None
    action_results: List[Dict] = field(default_factory=list)
    hr_comments: Optional[str] = None
    
    def to_dict(self) -> Dict:
        return {
            "id": self.id,
            "email_data": self.email_data,
            "classification": self.classification,
            "proposed_actions": self.proposed_actions,
            "suggested_response": self.suggested_response,
            "status": self.status,
            "created_at": self.created_at,
            "validation_request": self.validation_request,
            "action_results": self.action_results,
            "hr_comments": self.hr_comments
        }

# Complaint status constants
class ComplaintStatus:
    PENDING = "pending"
    PROCESSED = "processed"
    PENDING_VALIDATION = "pending_validation"
    HR_VALIDATED = "hr_validated"
    AUTO_RESOLVED = "auto_resolved"
    REJECTED = "rejected"

# Urgency levels
class UrgencyLevel:
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"

# Complaint categories
class ComplaintCategory:
    CIN_DUPLICATE = "cin_duplicate"
    INFO_MODIFICATION = "info_modification"
    PRINT_ISSUE = "print_issue"
    BROKEN_LINK = "broken_link"
    EMAIL_NOT_RECEIVED = "email_not_received"
    FILE_UPLOAD_ERROR = "file_upload_error"
    PROCESS_QUESTION = "process_question"
    OTHER = "other"
