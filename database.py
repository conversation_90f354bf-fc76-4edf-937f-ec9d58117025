"""
Database models and operations for the AI Complaint Management System
"""

import os
import json
import logging
from datetime import datetime
from typing import Dict, List, Optional, Any
import psycopg2
from psycopg2.extras import RealDictCursor
from psycopg2 import sql

logger = logging.getLogger(__name__)

class DatabaseManager:
    """Manages PostgreSQL database operations for complaints and candidates"""
    
    def __init__(self):
        self.connection_string = os.environ.get("DATABASE_URL")
        if not self.connection_string:
            # Try to construct from individual components
            host = os.environ.get("PGHOST", "localhost")
            port = os.environ.get("PGPORT", "5432")
            database = os.environ.get("PGDATABASE", "postgres")
            user = os.environ.get("PGUSER", "postgres")
            password = os.environ.get("PGPASSWORD", "")
            
            self.connection_string = f"postgresql://{user}:{password}@{host}:{port}/{database}"
        
        self._init_tables()
    
    def get_connection(self):
        """Get database connection"""
        return psycopg2.connect(self.connection_string)
    
    def _init_tables(self):
        """Initialize database tables"""
        try:
            with self.get_connection() as conn:
                with conn.cursor() as cur:
                    # Create candidates table
                    cur.execute("""
                        CREATE TABLE IF NOT EXISTS candidates (
                            cin VARCHAR(20) PRIMARY KEY,
                            name VARCHAR(255) NOT NULL,
                            email VARCHAR(255) UNIQUE NOT NULL,
                            status VARCHAR(50) DEFAULT 'registered',
                            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                        )
                    """)
                    
                    # Create complaints table
                    cur.execute("""
                        CREATE TABLE IF NOT EXISTS complaints (
                            id VARCHAR(50) PRIMARY KEY,
                            email_data JSONB NOT NULL,
                            classification JSONB NOT NULL,
                            proposed_actions JSONB DEFAULT '[]',
                            suggested_response JSONB DEFAULT '{}',
                            status VARCHAR(50) DEFAULT 'pending',
                            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                            validation_request JSONB DEFAULT NULL,
                            action_results JSONB DEFAULT '[]',
                            hr_comments TEXT DEFAULT NULL
                        )
                    """)
                    
                    # Create indexes for better performance
                    cur.execute("CREATE INDEX IF NOT EXISTS idx_complaints_status ON complaints(status)")
                    cur.execute("CREATE INDEX IF NOT EXISTS idx_complaints_created_at ON complaints(created_at)")
                    cur.execute("CREATE INDEX IF NOT EXISTS idx_candidates_email ON candidates(email)")
                    
                    conn.commit()
                    
                    # Insert some sample candidates if table is empty
                    cur.execute("SELECT COUNT(*) FROM candidates")
                    if cur.fetchone()[0] == 0:
                        sample_candidates = [
                            ("AB123456", "Ahmed Ben Ali", "<EMAIL>", "registered"),
                            ("CD789012", "Fatima Zahra", "<EMAIL>", "registered"),
                            ("EF456789", "Mohamed Amine", "<EMAIL>", "registered"),
                            ("GH123789", "Salma Bennani", "<EMAIL>", "registered")
                        ]
                        
                        for cin, name, email, status in sample_candidates:
                            cur.execute("""
                                INSERT INTO candidates (cin, name, email, status)
                                VALUES (%s, %s, %s, %s)
                                ON CONFLICT (cin) DO NOTHING
                            """, (cin, name, email, status))
                        
                        conn.commit()
                        logger.info("Sample candidates inserted")
            
            logger.info("Database tables initialized successfully")
            
        except Exception as e:
            logger.error(f"Error initializing database: {str(e)}")
            raise
    
    # Candidate operations
    def find_candidate_by_cin(self, cin: str) -> Optional[Dict]:
        """Find candidate by CIN"""
        try:
            with self.get_connection() as conn:
                with conn.cursor(cursor_factory=RealDictCursor) as cur:
                    cur.execute("SELECT * FROM candidates WHERE cin = %s", (cin,))
                    result = cur.fetchone()
                    return dict(result) if result else None
        except Exception as e:
            logger.error(f"Error finding candidate by CIN: {str(e)}")
            return None
    
    def find_candidate_by_email(self, email: str) -> Optional[Dict]:
        """Find candidate by email"""
        try:
            with self.get_connection() as conn:
                with conn.cursor(cursor_factory=RealDictCursor) as cur:
                    cur.execute("SELECT * FROM candidates WHERE email = %s", (email,))
                    result = cur.fetchone()
                    return dict(result) if result else None
        except Exception as e:
            logger.error(f"Error finding candidate by email: {str(e)}")
            return None
    
    def delete_candidate(self, cin: str) -> bool:
        """Delete candidate by CIN"""
        try:
            with self.get_connection() as conn:
                with conn.cursor() as cur:
                    cur.execute("DELETE FROM candidates WHERE cin = %s", (cin,))
                    deleted = cur.rowcount > 0
                    conn.commit()
                    return deleted
        except Exception as e:
            logger.error(f"Error deleting candidate: {str(e)}")
            return False
    
    def update_candidate(self, cin: str, updates: Dict) -> bool:
        """Update candidate information"""
        try:
            with self.get_connection() as conn:
                with conn.cursor() as cur:
                    # Build dynamic update query
                    set_clauses = []
                    values = []
                    
                    for key, value in updates.items():
                        if key in ['name', 'email', 'status']:
                            set_clauses.append(f"{key} = %s")
                            values.append(value)
                    
                    if not set_clauses:
                        return False
                    
                    query = f"UPDATE candidates SET {', '.join(set_clauses)} WHERE cin = %s"
                    values.append(cin)
                    
                    cur.execute(query, values)
                    updated = cur.rowcount > 0
                    conn.commit()
                    return updated
        except Exception as e:
            logger.error(f"Error updating candidate: {str(e)}")
            return False
    
    def get_all_candidates(self) -> List[Dict]:
        """Get all candidates"""
        try:
            with self.get_connection() as conn:
                with conn.cursor(cursor_factory=RealDictCursor) as cur:
                    cur.execute("SELECT * FROM candidates ORDER BY created_at DESC")
                    results = cur.fetchall()
                    return [dict(row) for row in results]
        except Exception as e:
            logger.error(f"Error getting candidates: {str(e)}")
            return []
    
    # Complaint operations
    def add_complaint(self, complaint_data: Dict) -> str:
        """Add new complaint and return complaint ID"""
        try:
            with self.get_connection() as conn:
                with conn.cursor() as cur:
                    # Generate complaint ID
                    cur.execute("SELECT COUNT(*) FROM complaints")
                    count = cur.fetchone()[0]
                    complaint_id = f"COMP_{count + 1:04d}"
                    
                    # Insert complaint
                    cur.execute("""
                        INSERT INTO complaints (
                            id, email_data, classification, proposed_actions,
                            suggested_response, status, validation_request, action_results, hr_comments
                        ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
                    """, (
                        complaint_id,
                        json.dumps(complaint_data.get('email_data', {})),
                        json.dumps(complaint_data.get('classification', {})),
                        json.dumps(complaint_data.get('proposed_actions', [])),
                        json.dumps(complaint_data.get('suggested_response', {})),
                        complaint_data.get('status', 'pending'),
                        json.dumps(complaint_data.get('validation_request')) if complaint_data.get('validation_request') else None,
                        json.dumps(complaint_data.get('action_results', [])),
                        complaint_data.get('hr_comments')
                    ))
                    
                    conn.commit()
                    return complaint_id
        except Exception as e:
            logger.error(f"Error adding complaint: {str(e)}")
            raise
    
    def get_complaint(self, complaint_id: str) -> Optional[Dict]:
        """Get complaint by ID"""
        try:
            with self.get_connection() as conn:
                with conn.cursor(cursor_factory=RealDictCursor) as cur:
                    cur.execute("SELECT * FROM complaints WHERE id = %s", (complaint_id,))
                    result = cur.fetchone()
                    if result:
                        complaint = dict(result)
                        # Convert timestamp to string for JSON serialization
                        if complaint.get('created_at'):
                            complaint['created_at'] = complaint['created_at'].isoformat()
                        return complaint
                    return None
        except Exception as e:
            logger.error(f"Error getting complaint: {str(e)}")
            return None
    
    def update_complaint(self, complaint_id: str, updates: Dict) -> bool:
        """Update complaint"""
        try:
            with self.get_connection() as conn:
                with conn.cursor() as cur:
                    # Build dynamic update query
                    set_clauses = []
                    values = []
                    
                    for key, value in updates.items():
                        if key in ['email_data', 'classification', 'proposed_actions', 'suggested_response', 'validation_request', 'action_results']:
                            set_clauses.append(f"{key} = %s")
                            values.append(json.dumps(value))
                        elif key in ['status', 'hr_comments']:
                            set_clauses.append(f"{key} = %s")
                            values.append(value)
                    
                    if not set_clauses:
                        return False
                    
                    query = f"UPDATE complaints SET {', '.join(set_clauses)} WHERE id = %s"
                    values.append(complaint_id)
                    
                    cur.execute(query, values)
                    updated = cur.rowcount > 0
                    conn.commit()
                    return updated
        except Exception as e:
            logger.error(f"Error updating complaint: {str(e)}")
            return False
    
    def get_all_complaints(self) -> List[Dict]:
        """Get all complaints"""
        try:
            with self.get_connection() as conn:
                with conn.cursor(cursor_factory=RealDictCursor) as cur:
                    cur.execute("SELECT * FROM complaints ORDER BY created_at DESC")
                    results = cur.fetchall()
                    complaints = []
                    for row in results:
                        complaint = dict(row)
                        # Convert timestamp to string for JSON serialization
                        if complaint.get('created_at'):
                            complaint['created_at'] = complaint['created_at'].isoformat()
                        complaints.append(complaint)
                    return complaints
        except Exception as e:
            logger.error(f"Error getting complaints: {str(e)}")
            return []

# Global database instance
db_manager = DatabaseManager()