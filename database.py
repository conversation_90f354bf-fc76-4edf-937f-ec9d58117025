"""
Database models and operations for the AI Complaint Management System
Supports both MySQL and Oracle databases with abstraction layer
"""

import os
import json
import logging
from datetime import datetime
from typing import Dict, List, Optional, Any
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

logger = logging.getLogger(__name__)

# Import all database modules with fallbacks
import sqlite3

try:
    import mysql.connector
    MYSQL_AVAILABLE = True
except ImportError:
    MYSQL_AVAILABLE = False

try:
    import cx_Oracle
    ORACLE_AVAILABLE = True
except ImportError:
    ORACLE_AVAILABLE = False

# Database error types
DatabaseError = sqlite3.Error

class DatabaseConnection:
    """Database connection abstraction layer"""

    def __init__(self):
        # Determine database type from environment
        self.db_type = os.getenv('DATABASE_TYPE', 'sqlite').lower()

        # Validate database type and availability
        if self.db_type == 'mysql' and not MYSQL_AVAILABLE:
            print("⚠️  MySQL requested but not available, falling back to SQLite")
            self.db_type = 'sqlite'
        elif self.db_type == 'oracle' and not ORACLE_AVAILABLE:
            print("⚠️  Oracle requested but not available, falling back to SQLite")
            self.db_type = 'sqlite'

        self.connection = None
        print(f"🗄️  Database type: {self.db_type.upper()}")

    def connect(self):
        """Establish database connection"""
        try:
            if self.db_type == 'mysql' and MYSQL_AVAILABLE:
                self.connection = mysql.connector.connect(
                    host=os.getenv('MYSQL_HOST', 'localhost'),
                    port=int(os.getenv('MYSQL_PORT', 3306)),
                    database=os.getenv('MYSQL_DATABASE', 'hr_complaints'),
                    user=os.getenv('MYSQL_USER', 'root'),
                    password=os.getenv('MYSQL_PASSWORD', ''),
                    autocommit=False
                )
            elif self.db_type == 'oracle' and ORACLE_AVAILABLE:
                import cx_Oracle
                dsn = cx_Oracle.makedsn(
                    host=os.getenv('ORACLE_HOST', 'localhost'),
                    port=int(os.getenv('ORACLE_PORT', 1521)),
                    service_name=os.getenv('ORACLE_SERVICE_NAME', 'XE')
                )
                self.connection = cx_Oracle.connect(
                    user=os.getenv('ORACLE_USER'),
                    password=os.getenv('ORACLE_PASSWORD'),
                    dsn=dsn
                )
            elif self.db_type == 'sqlite':
                db_path = os.getenv('SQLITE_DATABASE', 'hr_complaints.db')
                self.connection = sqlite3.connect(db_path)
                # Enable foreign keys and row factory for dict-like access
                self.connection.execute("PRAGMA foreign_keys = ON")
                self.connection.row_factory = sqlite3.Row

            logger.info(f"Connected to {self.db_type.upper()} database")
            return self.connection

        except Exception as e:
            logger.error(f"Database connection failed: {str(e)}")
            raise

    def get_cursor(self):
        """Get database cursor with appropriate configuration"""
        if not self.connection:
            self.connect()

        if self.db_type == 'mysql':
            return self.connection.cursor(dictionary=True)
        elif self.db_type == 'oracle':
            cursor = self.connection.cursor()
            cursor.rowfactory = lambda *args: dict(zip([d[0].lower() for d in cursor.description], args))
            return cursor
        elif self.db_type == 'sqlite':
            return self.connection.cursor()

    def close(self):
        """Close database connection"""
        if self.connection:
            self.connection.close()
            self.connection = None

class DatabaseManager:
    """Manages database operations for complaints and candidates with MySQL/Oracle support"""

    def __init__(self):
        self.db_connection = DatabaseConnection()
        self.db_type = self.db_connection.db_type
        self._init_tables()

    def get_connection(self):
        """Get database connection"""
        return self.db_connection.connect()

    def _get_sql_syntax(self):
        """Get database-specific SQL syntax"""
        if self.db_type == 'mysql':
            return {
                'json_type': 'JSON',
                'text_type': 'TEXT',
                'varchar_type': 'VARCHAR',
                'timestamp_type': 'TIMESTAMP',
                'auto_increment': 'AUTO_INCREMENT',
                'current_timestamp': 'CURRENT_TIMESTAMP',
                'json_extract': lambda col, path: f"JSON_EXTRACT({col}, '$.{path}')",
                'limit': lambda n: f"LIMIT {n}"
            }
        elif self.db_type == 'oracle':
            return {
                'json_type': 'CLOB',
                'text_type': 'CLOB',
                'varchar_type': 'VARCHAR2',
                'timestamp_type': 'TIMESTAMP',
                'auto_increment': '',  # Oracle uses sequences
                'current_timestamp': 'CURRENT_TIMESTAMP',
                'json_extract': lambda col, path: f"JSON_VALUE({col}, '$.{path}')",
                'limit': lambda n: f"ROWNUM <= {n}"
            }
        elif self.db_type == 'sqlite':
            return {
                'json_type': 'TEXT',  # SQLite stores JSON as TEXT
                'text_type': 'TEXT',
                'varchar_type': 'TEXT',
                'timestamp_type': 'DATETIME',
                'auto_increment': 'AUTOINCREMENT',
                'current_timestamp': 'CURRENT_TIMESTAMP',
                'json_extract': lambda col, path: f"JSON_EXTRACT({col}, '$.{path}')",
                'limit': lambda n: f"LIMIT {n}"
            }
    
    def _init_tables(self):
        """Initialize database tables"""
        try:
            syntax = self._get_sql_syntax()
            conn = self.get_connection()
            cursor = self.db_connection.get_cursor()

            # Create candidates table
            if self.db_type == 'mysql':
                candidates_sql = f"""
                    CREATE TABLE IF NOT EXISTS candidates (
                        cin {syntax['varchar_type']}(20) PRIMARY KEY,
                        name {syntax['varchar_type']}(255) NOT NULL,
                        email {syntax['varchar_type']}(255) UNIQUE NOT NULL,
                        status {syntax['varchar_type']}(50) DEFAULT 'registered',
                        created_at {syntax['timestamp_type']} DEFAULT {syntax['current_timestamp']}
                    )
                """
            elif self.db_type == 'oracle':
                candidates_sql = f"""
                    CREATE TABLE candidates (
                        cin {syntax['varchar_type']}(20) PRIMARY KEY,
                        name {syntax['varchar_type']}(255) NOT NULL,
                        email {syntax['varchar_type']}(255) UNIQUE NOT NULL,
                        status {syntax['varchar_type']}(50) DEFAULT 'registered',
                        created_at {syntax['timestamp_type']} DEFAULT {syntax['current_timestamp']}
                    )
                """
            elif self.db_type == 'sqlite':
                candidates_sql = f"""
                    CREATE TABLE IF NOT EXISTS candidates (
                        cin {syntax['varchar_type']} PRIMARY KEY,
                        name {syntax['varchar_type']} NOT NULL,
                        email {syntax['varchar_type']} UNIQUE NOT NULL,
                        status {syntax['varchar_type']} DEFAULT 'registered',
                        created_at {syntax['timestamp_type']} DEFAULT {syntax['current_timestamp']}
                    )
                """

            try:
                cursor.execute(candidates_sql)
            except DatabaseError as e:
                if "already exists" not in str(e).lower():
                    raise

            # Create complaints table
            if self.db_type == 'mysql':
                complaints_sql = f"""
                    CREATE TABLE IF NOT EXISTS complaints (
                        id {syntax['varchar_type']}(50) PRIMARY KEY,
                        email_data {syntax['json_type']} NOT NULL,
                        classification {syntax['json_type']} NOT NULL,
                        proposed_actions {syntax['json_type']} DEFAULT ('[]'),
                        suggested_response {syntax['json_type']} DEFAULT ('{{}}'),
                        status {syntax['varchar_type']}(50) DEFAULT 'pending',
                        created_at {syntax['timestamp_type']} DEFAULT {syntax['current_timestamp']},
                        validation_request {syntax['json_type']} DEFAULT NULL,
                        action_results {syntax['json_type']} DEFAULT ('[]'),
                        hr_comments {syntax['text_type']} DEFAULT NULL
                    )
                """
            elif self.db_type == 'oracle':
                complaints_sql = f"""
                    CREATE TABLE complaints (
                        id {syntax['varchar_type']}(50) PRIMARY KEY,
                        email_data {syntax['json_type']} NOT NULL,
                        classification {syntax['json_type']} NOT NULL,
                        proposed_actions {syntax['json_type']} DEFAULT '[]',
                        suggested_response {syntax['json_type']} DEFAULT '{{}}',
                        status {syntax['varchar_type']}(50) DEFAULT 'pending',
                        created_at {syntax['timestamp_type']} DEFAULT {syntax['current_timestamp']},
                        validation_request {syntax['json_type']} DEFAULT NULL,
                        action_results {syntax['json_type']} DEFAULT '[]',
                        hr_comments {syntax['text_type']} DEFAULT NULL
                    )
                """
            elif self.db_type == 'sqlite':
                complaints_sql = f"""
                    CREATE TABLE IF NOT EXISTS complaints (
                        id {syntax['varchar_type']} PRIMARY KEY,
                        email_data {syntax['json_type']} NOT NULL,
                        classification {syntax['json_type']} NOT NULL,
                        proposed_actions {syntax['json_type']} DEFAULT '[]',
                        suggested_response {syntax['json_type']} DEFAULT '{{}}',
                        status {syntax['varchar_type']} DEFAULT 'pending',
                        created_at {syntax['timestamp_type']} DEFAULT {syntax['current_timestamp']},
                        validation_request {syntax['json_type']} DEFAULT NULL,
                        action_results {syntax['json_type']} DEFAULT '[]',
                        hr_comments {syntax['text_type']} DEFAULT NULL
                    )
                """

            try:
                cursor.execute(complaints_sql)
            except DatabaseError as e:
                if "already exists" not in str(e).lower():
                    raise

            # Create indexes for better performance
            indexes = [
                "CREATE INDEX idx_complaints_status ON complaints(status)",
                "CREATE INDEX idx_complaints_created_at ON complaints(created_at)",
                "CREATE INDEX idx_candidates_email ON candidates(email)"
            ]

            for index_sql in indexes:
                try:
                    cursor.execute(index_sql)
                except DatabaseError as e:
                    if "already exists" not in str(e).lower():
                        logger.warning(f"Index creation warning: {str(e)}")

            conn.commit()

            # Insert sample candidates if table is empty
            cursor.execute("SELECT COUNT(*) FROM candidates")
            result = cursor.fetchone()
            count = result[0] if isinstance(result, tuple) else result['count(*)']

            if count == 0:
                sample_candidates = [
                    ("AB123456", "Ahmed Ben Ali", "<EMAIL>", "registered"),
                    ("CD789012", "Fatima Zahra", "<EMAIL>", "registered"),
                    ("EF456789", "Mohamed Amine", "<EMAIL>", "registered"),
                    ("GH123789", "Salma Bennani", "<EMAIL>", "registered")
                ]

                for cin, name, email, status in sample_candidates:
                    if self.db_type == 'mysql':
                        insert_sql = """
                            INSERT INTO candidates (cin, name, email, status)
                            VALUES (%s, %s, %s, %s)
                            ON DUPLICATE KEY UPDATE cin=cin
                        """
                    elif self.db_type == 'oracle':
                        insert_sql = """
                            INSERT INTO candidates (cin, name, email, status)
                            VALUES (:1, :2, :3, :4)
                        """
                    elif self.db_type == 'sqlite':
                        insert_sql = """
                            INSERT OR IGNORE INTO candidates (cin, name, email, status)
                            VALUES (?, ?, ?, ?)
                        """

                    try:
                        cursor.execute(insert_sql, (cin, name, email, status))
                    except DatabaseError as e:
                        if "unique constraint" not in str(e).lower():
                            logger.warning(f"Sample data insertion warning: {str(e)}")

                conn.commit()
                logger.info("Sample candidates inserted")

            cursor.close()
            conn.close()
            logger.info("Database tables initialized successfully")

        except Exception as e:
            logger.error(f"Error initializing database: {str(e)}")
            raise
    
    # Candidate operations
    def find_candidate_by_cin(self, cin: str) -> Optional[Dict]:
        """Find candidate by CIN"""
        try:
            conn = self.get_connection()
            cursor = self.db_connection.get_cursor()

            if self.db_type == 'mysql':
                cursor.execute("SELECT * FROM candidates WHERE cin = %s", (cin,))
            elif self.db_type == 'oracle':
                cursor.execute("SELECT * FROM candidates WHERE cin = :1", (cin,))
            elif self.db_type == 'sqlite':
                cursor.execute("SELECT * FROM candidates WHERE cin = ?", (cin,))

            result = cursor.fetchone()
            cursor.close()
            conn.close()

            return dict(result) if result else None
        except Exception as e:
            logger.error(f"Error finding candidate by CIN: {str(e)}")
            return None

    def find_candidate_by_email(self, email: str) -> Optional[Dict]:
        """Find candidate by email"""
        try:
            conn = self.get_connection()
            cursor = self.db_connection.get_cursor()

            if self.db_type == 'mysql':
                cursor.execute("SELECT * FROM candidates WHERE email = %s", (email,))
            elif self.db_type == 'oracle':
                cursor.execute("SELECT * FROM candidates WHERE email = :1", (email,))
            elif self.db_type == 'sqlite':
                cursor.execute("SELECT * FROM candidates WHERE email = ?", (email,))

            result = cursor.fetchone()
            cursor.close()
            conn.close()

            return dict(result) if result else None
        except Exception as e:
            logger.error(f"Error finding candidate by email: {str(e)}")
            return None
    
    def delete_candidate(self, cin: str) -> bool:
        """Delete candidate by CIN"""
        try:
            conn = self.get_connection()
            cursor = self.db_connection.get_cursor()

            if self.db_type == 'mysql':
                cursor.execute("DELETE FROM candidates WHERE cin = %s", (cin,))
            elif self.db_type == 'oracle':
                cursor.execute("DELETE FROM candidates WHERE cin = :1", (cin,))
            elif self.db_type == 'sqlite':
                cursor.execute("DELETE FROM candidates WHERE cin = ?", (cin,))

            deleted = cursor.rowcount > 0
            conn.commit()
            cursor.close()
            conn.close()
            return deleted
        except Exception as e:
            logger.error(f"Error deleting candidate: {str(e)}")
            return False

    def update_candidate(self, cin: str, updates: Dict) -> bool:
        """Update candidate information"""
        try:
            conn = self.get_connection()
            cursor = self.db_connection.get_cursor()

            # Build dynamic update query
            set_clauses = []
            values = []

            for key, value in updates.items():
                if key in ['name', 'email', 'status']:
                    if self.db_type == 'mysql':
                        set_clauses.append(f"{key} = %s")
                    elif self.db_type == 'oracle':
                        set_clauses.append(f"{key} = :{len(values) + 1}")
                    elif self.db_type == 'sqlite':
                        set_clauses.append(f"{key} = ?")
                    values.append(value)

            if not set_clauses:
                cursor.close()
                conn.close()
                return False

            if self.db_type == 'mysql':
                query = f"UPDATE candidates SET {', '.join(set_clauses)} WHERE cin = %s"
                values.append(cin)
            elif self.db_type == 'oracle':
                query = f"UPDATE candidates SET {', '.join(set_clauses)} WHERE cin = :{len(values) + 1}"
                values.append(cin)
            elif self.db_type == 'sqlite':
                query = f"UPDATE candidates SET {', '.join(set_clauses)} WHERE cin = ?"
                values.append(cin)

            cursor.execute(query, values)
            updated = cursor.rowcount > 0
            conn.commit()
            cursor.close()
            conn.close()
            return updated
        except Exception as e:
            logger.error(f"Error updating candidate: {str(e)}")
            return False

    def get_all_candidates(self) -> List[Dict]:
        """Get all candidates"""
        try:
            conn = self.get_connection()
            cursor = self.db_connection.get_cursor()

            cursor.execute("SELECT * FROM candidates ORDER BY created_at DESC")
            results = cursor.fetchall()
            cursor.close()
            conn.close()

            return [dict(row) for row in results]
        except Exception as e:
            logger.error(f"Error getting candidates: {str(e)}")
            return []
    
    # Complaint operations
    def add_complaint(self, complaint_data: Dict) -> str:
        """Add new complaint and return complaint ID"""
        try:
            conn = self.get_connection()
            cursor = self.db_connection.get_cursor()

            # Generate complaint ID
            cursor.execute("SELECT COUNT(*) FROM complaints")
            result = cursor.fetchone()
            count = result[0] if isinstance(result, tuple) else result['count(*)']
            complaint_id = f"COMP_{count + 1:04d}"

            # Prepare data
            email_data_json = json.dumps(complaint_data.get('email_data', {}))
            classification_json = json.dumps(complaint_data.get('classification', {}))
            proposed_actions_json = json.dumps(complaint_data.get('proposed_actions', []))
            suggested_response_json = json.dumps(complaint_data.get('suggested_response', {}))
            validation_request_json = json.dumps(complaint_data.get('validation_request')) if complaint_data.get('validation_request') else None
            action_results_json = json.dumps(complaint_data.get('action_results', []))

            # Insert complaint
            if self.db_type == 'mysql':
                cursor.execute("""
                    INSERT INTO complaints (
                        id, email_data, classification, proposed_actions,
                        suggested_response, status, validation_request, action_results, hr_comments
                    ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
                """, (
                    complaint_id,
                    email_data_json,
                    classification_json,
                    proposed_actions_json,
                    suggested_response_json,
                    complaint_data.get('status', 'pending'),
                    validation_request_json,
                    action_results_json,
                    complaint_data.get('hr_comments')
                ))
            elif self.db_type == 'oracle':
                cursor.execute("""
                    INSERT INTO complaints (
                        id, email_data, classification, proposed_actions,
                        suggested_response, status, validation_request, action_results, hr_comments
                    ) VALUES (:1, :2, :3, :4, :5, :6, :7, :8, :9)
                """, (
                    complaint_id,
                    email_data_json,
                    classification_json,
                    proposed_actions_json,
                    suggested_response_json,
                    complaint_data.get('status', 'pending'),
                    validation_request_json,
                    action_results_json,
                    complaint_data.get('hr_comments')
                ))
            elif self.db_type == 'sqlite':
                cursor.execute("""
                    INSERT INTO complaints (
                        id, email_data, classification, proposed_actions,
                        suggested_response, status, validation_request, action_results, hr_comments
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    complaint_id,
                    email_data_json,
                    classification_json,
                    proposed_actions_json,
                    suggested_response_json,
                    complaint_data.get('status', 'pending'),
                    validation_request_json,
                    action_results_json,
                    complaint_data.get('hr_comments')
                ))

            conn.commit()
            cursor.close()
            conn.close()
            return complaint_id
        except Exception as e:
            logger.error(f"Error adding complaint: {str(e)}")
            raise
    
    def get_complaint(self, complaint_id: str) -> Optional[Dict]:
        """Get complaint by ID"""
        try:
            conn = self.get_connection()
            cursor = self.db_connection.get_cursor()

            if self.db_type == 'mysql':
                cursor.execute("SELECT * FROM complaints WHERE id = %s", (complaint_id,))
            elif self.db_type == 'oracle':
                cursor.execute("SELECT * FROM complaints WHERE id = :1", (complaint_id,))
            elif self.db_type == 'sqlite':
                cursor.execute("SELECT * FROM complaints WHERE id = ?", (complaint_id,))

            result = cursor.fetchone()
            cursor.close()
            conn.close()

            if result:
                complaint = dict(result)
                # Convert timestamp to string for JSON serialization
                if complaint.get('created_at'):
                    complaint['created_at'] = complaint['created_at'].isoformat()
                return complaint
            return None
        except Exception as e:
            logger.error(f"Error getting complaint: {str(e)}")
            return None
    
    def update_complaint(self, complaint_id: str, updates: Dict) -> bool:
        """Update complaint"""
        try:
            with self.get_connection() as conn:
                with conn.cursor() as cur:
                    # Build dynamic update query
                    set_clauses = []
                    values = []
                    
                    for key, value in updates.items():
                        if key in ['email_data', 'classification', 'proposed_actions', 'suggested_response', 'validation_request', 'action_results']:
                            set_clauses.append(f"{key} = %s")
                            values.append(json.dumps(value))
                        elif key in ['status', 'hr_comments']:
                            set_clauses.append(f"{key} = %s")
                            values.append(value)
                    
                    if not set_clauses:
                        return False
                    
                    query = f"UPDATE complaints SET {', '.join(set_clauses)} WHERE id = %s"
                    values.append(complaint_id)
                    
                    cur.execute(query, values)
                    updated = cur.rowcount > 0
                    conn.commit()
                    return updated
        except Exception as e:
            logger.error(f"Error updating complaint: {str(e)}")
            return False
    
    def get_all_complaints(self) -> List[Dict]:
        """Get all complaints"""
        try:
            conn = self.get_connection()
            cursor = self.db_connection.get_cursor()

            cursor.execute("SELECT * FROM complaints ORDER BY created_at DESC")
            results = cursor.fetchall()
            cursor.close()
            conn.close()

            complaints = []
            for row in results:
                complaint = dict(row)
                # Convert timestamp to string for JSON serialization
                if complaint.get('created_at'):
                    complaint['created_at'] = complaint['created_at'].isoformat()
                complaints.append(complaint)
            return complaints
        except Exception as e:
            logger.error(f"Error getting complaints: {str(e)}")
            return []

# Global database instance
db_manager = DatabaseManager()