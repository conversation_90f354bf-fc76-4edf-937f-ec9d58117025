# File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.

from typing import List, Optional
from typing_extensions import Literal

from ..._models import BaseModel
from .chat_completion_message_tool_call import ChatCompletionMessageToolCall

__all__ = [
    "ChatCompletionMessage",
    "ExecutedTool",
    "ExecutedToolCodeResult",
    "ExecutedToolSearchResults",
    "ExecutedToolSearchResultsResult",
    "FunctionCall",
]


class ExecutedToolCodeResult(BaseModel):
    png: Optional[str] = None
    """Base64 encoded PNG image output from code execution"""

    text: Optional[str] = None
    """The text version of the code execution result"""


class ExecutedToolSearchResultsResult(BaseModel):
    content: Optional[str] = None
    """The content of the search result"""

    score: Optional[float] = None
    """The relevance score of the search result"""

    title: Optional[str] = None
    """The title of the search result"""

    url: Optional[str] = None
    """The URL of the search result"""


class ExecutedToolSearchResults(BaseModel):
    images: Optional[List[str]] = None
    """List of image URLs returned by the search"""

    results: Optional[List[ExecutedToolSearchResultsResult]] = None
    """List of search results"""


class ExecutedTool(BaseModel):
    arguments: str
    """The arguments passed to the tool in JSON format."""

    index: int
    """The index of the executed tool."""

    type: str
    """The type of tool that was executed."""

    code_results: Optional[List[ExecutedToolCodeResult]] = None
    """Array of code execution results"""

    output: Optional[str] = None
    """The output returned by the tool."""

    search_results: Optional[ExecutedToolSearchResults] = None
    """The search results returned by the tool, if applicable."""


class FunctionCall(BaseModel):
    arguments: str
    """
    The arguments to call the function with, as generated by the model in JSON
    format. Note that the model does not always generate valid JSON, and may
    hallucinate parameters not defined by your function schema. Validate the
    arguments in your code before calling your function.
    """

    name: str
    """The name of the function to call."""


class ChatCompletionMessage(BaseModel):
    content: Optional[str] = None
    """The contents of the message."""

    role: Literal["assistant"]
    """The role of the author of this message."""

    executed_tools: Optional[List[ExecutedTool]] = None
    """
    A list of tools that were executed during the chat completion for compound AI
    systems.
    """

    function_call: Optional[FunctionCall] = None
    """Deprecated and replaced by `tool_calls`.

    The name and arguments of a function that should be called, as generated by the
    model.
    """

    reasoning: Optional[str] = None
    """The model's reasoning for a response.

    Only available for reasoning models when requests parameter reasoning_format has
    value `parsed.
    """

    tool_calls: Optional[List[ChatCompletionMessageToolCall]] = None
    """The tool calls generated by the model, such as function calls."""
