[project]
name = "ai-team-orchestrator"
version = "0.1.0"
description = "AI Team Orchestrator - Multi-Agent Complaint Management System for HR Recruitment"
requires-python = ">=3.11"
dependencies = [
    "email-validator>=2.2.0",
    "flask>=3.1.1",
    "flask-sqlalchemy>=3.1.1",
    "groq>=0.26.0",
    "gunicorn>=23.0.0",
    "openai>=1.82.1",
    "mysql-connector-python>=8.0.33",
    "cx-oracle>=8.3.0",
    "werkzeug>=3.1.3",
    "langgraph>=0.0.55",
    "langchain>=0.1.0",
    "langchain-community>=0.0.20",
    "imaplib2>=3.6",
    "email-reply-parser>=0.5.12",
    "python-dotenv>=1.0.0",
    "schedule>=1.2.0",
    "smtplib2>=0.2.1",
]
