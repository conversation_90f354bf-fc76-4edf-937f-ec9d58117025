<!DOCTYPE html>
<html lang="en" data-bs-theme="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Complaint Management System</title>
    <link href="https://cdn.replit.com/agent/bootstrap-agent-dark-theme.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/custom.css') }}" rel="stylesheet">
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="{{ url_for('index') }}">
                <i class="fas fa-robot me-2"></i>
                AI Complaint Management
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="{{ url_for('email_monitor_dashboard') }}">
                    <i class="fas fa-envelope-open-text me-1"></i>Email Monitor
                </a>
                <span class="navbar-text">
                    Multi-Agent HR System
                </span>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- Flash Messages -->
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'danger' if category == 'error' else 'success' if category == 'success' else 'info' }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        <!-- Dashboard Stats -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card bg-primary">
                    <div class="card-body text-center">
                        <i class="fas fa-inbox fa-2x mb-2"></i>
                        <h3>{{ stats.total }}</h3>
                        <p class="mb-0">Total Complaints</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-warning">
                    <div class="card-body text-center">
                        <i class="fas fa-clock fa-2x mb-2"></i>
                        <h3>{{ stats.pending_validation }}</h3>
                        <p class="mb-0">Pending Validation</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-success">
                    <div class="card-body text-center">
                        <i class="fas fa-check-circle fa-2x mb-2"></i>
                        <h3>{{ stats.auto_resolved }}</h3>
                        <p class="mb-0">Auto Resolved</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-info">
                    <div class="card-body text-center">
                        <i class="fas fa-user-check fa-2x mb-2"></i>
                        <h3>{{ stats.hr_validated }}</h3>
                        <p class="mb-0">HR Validated</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- New Complaint Form -->
        <div class="card mb-4">
            <div class="card-header">
                <h5><i class="fas fa-plus-circle me-2"></i>Submit New Complaint</h5>
            </div>
            <div class="card-body">
                <form method="POST" action="{{ url_for('submit_complaint') }}">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="sender_email" class="form-label">Sender Email</label>
                            <input type="email" class="form-control" id="sender_email" name="sender_email" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="subject" class="form-label">Subject</label>
                            <input type="text" class="form-control" id="subject" name="subject" placeholder="Optional">
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="email_content" class="form-label">Email Content</label>
                        <textarea class="form-control" id="email_content" name="email_content" rows="4" required
                                  placeholder="Enter the complaint email content here..."></textarea>
                    </div>
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="use_langgraph" name="use_langgraph" value="true" checked>
                            <label class="form-check-label" for="use_langgraph">
                                <i class="fas fa-project-diagram me-1"></i>Use LangGraph Orchestrator
                                <small class="text-muted d-block">Enhanced multi-agent workflow with better state management</small>
                            </label>
                        </div>
                    </div>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-paper-plane me-2"></i>Process Complaint
                    </button>
                </form>
            </div>
        </div>

        <!-- Complaints List -->
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5><i class="fas fa-list me-2"></i>Recent Complaints</h5>
                <button class="btn btn-outline-secondary btn-sm" onclick="refreshComplaints()">
                    <i class="fas fa-sync-alt me-1"></i>Refresh
                </button>
            </div>
            <div class="card-body">
                {% if complaints %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Date</th>
                                    <th>Sender</th>
                                    <th>Category</th>
                                    <th>Urgency</th>
                                    <th>Status</th>
                                    <th>Confidence</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for complaint in complaints %}
                                <tr>
                                    <td>
                                        <code>{{ complaint.id }}</code>
                                    </td>
                                    <td>
                                        {{ complaint.created_at.split('T')[0] }}<br>
                                        <small class="text-muted">{{ complaint.created_at.split('T')[1].split('.')[0] }}</small>
                                    </td>
                                    <td>
                                        {{ complaint.email_data.sender_email }}<br>
                                        <small class="text-muted">{{ complaint.email_data.subject or 'No subject' }}</small>
                                    </td>
                                    <td>
                                        <span class="badge bg-secondary">
                                            {{ complaint.classification.category.replace('_', ' ').title() }}
                                        </span>
                                    </td>
                                    <td>
                                        {% set urgency = complaint.classification.urgency %}
                                        <span class="badge bg-{{ 'danger' if urgency == 'high' else 'warning' if urgency == 'medium' else 'success' }}">
                                            {{ urgency.title() }}
                                        </span>
                                    </td>
                                    <td>
                                        {% set status = complaint.status %}
                                        <span class="badge bg-{{ 'warning' if status == 'pending_validation' else 'success' if status in ['auto_resolved', 'hr_validated'] else 'secondary' }}">
                                            {{ status.replace('_', ' ').title() }}
                                        </span>
                                    </td>
                                    <td>
                                        {% set confidence = complaint.classification.confidence %}
                                        <div class="progress" style="width: 60px;">
                                            <div class="progress-bar bg-{{ 'success' if confidence > 0.8 else 'warning' if confidence > 0.5 else 'danger' }}" 
                                                 style="width: {{ (confidence * 100)|round }}%"></div>
                                        </div>
                                        <small>{{ (confidence * 100)|round }}%</small>
                                    </td>
                                    <td>
                                        <a href="{{ url_for('complaint_detail', complaint_id=complaint.id) }}" 
                                           class="btn btn-outline-primary btn-sm">
                                            <i class="fas fa-eye me-1"></i>View
                                        </a>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">No complaints yet</h5>
                        <p class="text-muted">Submit a new complaint using the form above to get started.</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="{{ url_for('static', filename='js/main.js') }}"></script>
</body>
</html>
