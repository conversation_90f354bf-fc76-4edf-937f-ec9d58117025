import json
import os
import logging
from datetime import datetime
from typing import Dict, List, Any, Optional
from groq import Groq
from database import db_manager

# Initialize Groq client
GROQ_API_KEY = "********************************************************"
groq_client = Groq(api_key=GROQ_API_KEY)

logger = logging.getLogger(__name__)

class BaseAgent:
    """Base class for all agents"""
    
    def __init__(self, name: str):
        self.name = name
        self.logger = logging.getLogger(f"Agent.{name}")
    
    def log_action(self, action: str, details: Dict = None):
        """Log agent action"""
        log_entry = {
            "agent": self.name,
            "action": action,
            "timestamp": datetime.now().isoformat(),
            "details": details or {}
        }
        self.logger.info(f"Agent {self.name}: {action}")
        return log_entry

class AgentMailReader(BaseAgent):
    """Agent responsible for reading and parsing email complaints"""
    
    def __init__(self):
        super().__init__("MailReader")
    
    def process_email(self, email_content: str, sender_email: str, subject: str) -> Dict:
        """Process incoming email and extract metadata"""
        try:
            # Extract basic information
            email_data = {
                "content": email_content,
                "sender_email": sender_email,
                "subject": subject,
                "processed_at": datetime.now().isoformat()
            }
            
            # Try to extract CIN from content using regex or simple parsing
            import re
            cin_pattern = r'\b[A-Z]{2}\d{6}\b'
            cin_matches = re.findall(cin_pattern, email_content.upper())
            
            if cin_matches:
                email_data["extracted_cin"] = cin_matches[0]
            
            self.log_action("email_processed", {"sender": sender_email, "subject": subject})
            return email_data
            
        except Exception as e:
            self.logger.error(f"Error processing email: {str(e)}")
            raise

class AgentClassifier(BaseAgent):
    """Agent responsible for classifying complaint types using AI"""
    
    def __init__(self):
        super().__init__("Classifier")
        self.complaint_categories = {
            "cin_duplicate": "CIN already used - candidate wants to re-register",
            "info_modification": "Information modification - candidate made error in registration",
            "print_issue": "Print button not working - PDF/browser issue",
            "broken_link": "Broken link or button - technical navigation issue", 
            "email_not_received": "Confirmation email not received",
            "file_upload_error": "Document upload or attachment error",
            "process_question": "Question about contest rules or process",
            "other": "Other/unclassified issue"
        }
    
    def classify_complaint(self, email_data: Dict) -> Dict:
        """Classify complaint using Groq API"""
        try:
            prompt = f"""
            Analyze this recruitment contest complaint email and classify it.
            
            Email content: {email_data['content']}
            Subject: {email_data.get('subject', '')}
            
            Categories available:
            {json.dumps(self.complaint_categories, indent=2)}
            
            Extract the following information in JSON format:
            {{
                "category": "primary_category_key",
                "urgency": "low|medium|high", 
                "extracted_info": {{
                    "cin": "extracted_cin_if_found",
                    "name": "extracted_name_if_found",
                    "specific_issue": "specific_technical_or_process_issue"
                }},
                "confidence": 0.0-1.0,
                "reasoning": "brief_explanation_of_classification"
            }}
            """
            
            response = groq_client.chat.completions.create(
                model="llama-3.3-70b-versatile",
                messages=[
                    {"role": "system", "content": "You are an expert at analyzing customer service complaints for recruitment contests. Respond only with valid JSON."},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.3
            )
            
            response_content = response.choices[0].message.content
            if response_content:
                response_content = response_content.strip()
                
                # Try to extract JSON from the response if it's wrapped in text
                if not response_content.startswith('{'):
                    # Look for JSON content within the response
                    import re
                    json_match = re.search(r'\{.*\}', response_content, re.DOTALL)
                    if json_match:
                        response_content = json_match.group()
                
                classification = json.loads(response_content)
            else:
                raise ValueError("Empty response from Groq API")
            
            # Merge with email data
            classification.update({
                "original_email": email_data,
                "classified_at": datetime.now().isoformat()
            })
            
            self.log_action("complaint_classified", {
                "category": classification["category"],
                "urgency": classification["urgency"],
                "confidence": classification["confidence"]
            })
            
            return classification
            
        except Exception as e:
            self.logger.error(f"Error classifying complaint: {str(e)}")
            # Fallback classification
            return {
                "category": "other",
                "urgency": "medium",
                "extracted_info": {"cin": email_data.get("extracted_cin")},
                "confidence": 0.1,
                "reasoning": f"Classification failed: {str(e)}",
                "original_email": email_data,
                "classified_at": datetime.now().isoformat()
            }

class AgentDatabaseHandler(BaseAgent):
    """Agent responsible for database operations"""
    
    def __init__(self):
        super().__init__("DatabaseHandler")
    
    def execute_action(self, action: str, data: Dict) -> Dict:
        """Execute database action based on classification"""
        try:
            result = {"success": False, "message": "", "data": None}
            
            if action == "find_candidate":
                cin = data.get("cin")
                email = data.get("email")
                
                candidate = None
                if cin:
                    candidate = db_manager.find_candidate_by_cin(cin)
                elif email:
                    candidate = db_manager.find_candidate_by_email(email)
                
                if candidate:
                    result = {
                        "success": True,
                        "message": "Candidate found",
                        "data": candidate
                    }
                else:
                    result = {
                        "success": False,
                        "message": "Candidate not found",
                        "data": None
                    }
            
            elif action == "delete_candidate":
                cin = data.get("cin")
                if cin and db_manager.delete_candidate(cin):
                    result = {
                        "success": True,
                        "message": f"Candidate {cin} deleted successfully",
                        "data": {"deleted_cin": cin}
                    }
                else:
                    result = {
                        "success": False,
                        "message": "Failed to delete candidate or candidate not found",
                        "data": None
                    }
            
            elif action == "update_candidate":
                cin = data.get("cin")
                updates = data.get("updates", {})
                if cin and db_manager.update_candidate(cin, updates):
                    result = {
                        "success": True,
                        "message": f"Candidate {cin} updated successfully",
                        "data": {"updated_cin": cin, "updates": updates}
                    }
                else:
                    result = {
                        "success": False,
                        "message": "Failed to update candidate",
                        "data": None
                    }
            
            self.log_action("database_action", {
                "action": action,
                "success": result["success"],
                "message": result["message"]
            })
            
            return result
            
        except Exception as e:
            self.logger.error(f"Database action failed: {str(e)}")
            return {
                "success": False,
                "message": f"Database error: {str(e)}",
                "data": None
            }

class AgentResponseGenerator(BaseAgent):
    """Agent responsible for generating personalized responses"""
    
    def __init__(self):
        super().__init__("ResponseGenerator")
    
    def generate_response(self, classification: Dict, db_result: Dict = None) -> Dict:
        """Generate personalized response based on classification"""
        try:
            category = classification["category"]
            extracted_info = classification.get("extracted_info", {})
            
            # Pre-defined response templates
            templates = {
                "cin_duplicate": """
                Bonjour,
                
                Nous avons bien reçu votre demande concernant votre inscription au concours.
                Votre ancien enregistrement a été supprimé de notre système.
                
                Vous pouvez maintenant vous réinscrire en utilisant le lien suivant : [LIEN_INSCRIPTION]
                
                Cordialement,
                L'équipe RH
                """,
                
                "print_issue": """
                Bonjour,
                
                Concernant le problème d'impression de votre fiche de candidature :
                
                • Utilisez un navigateur web (Chrome, Firefox, ou Edge) 
                • Évitez d'ouvrir le PDF dans un lecteur externe
                • Essayez Ctrl+P directement depuis le navigateur
                • Si le problème persiste, téléchargez d'abord le fichier puis imprimez
                
                Cordialement,
                L'équipe RH
                """,
                
                "info_modification": """
                Bonjour,
                
                Nous avons bien noté votre demande de modification d'informations.
                Votre dossier sera mis à jour dans les plus brefs délais.
                
                Vous recevrez un email de confirmation une fois la modification effectuée.
                
                Cordialement,
                L'équipe RH
                """,
                
                "broken_link": """
                Bonjour,
                
                Nous avons bien noté le problème technique que vous rencontrez.
                Notre équipe technique va examiner le lien/bouton défaillant.
                
                En attendant, vous pouvez essayer :
                • Vider le cache de votre navigateur
                • Utiliser un autre navigateur
                • Contacter notre support technique
                
                Cordialement,
                L'équipe RH
                """,
                
                "email_not_received": """
                Bonjour,
                
                Concernant l'email de confirmation non reçu :
                
                • Vérifiez votre dossier spam/courrier indésirable
                • Ajoutez notre adresse à vos contacts : <EMAIL>
                • L'email peut prendre jusqu'à 24h pour arriver
                
                Si le problème persiste après 24h, contactez-nous directement.
                
                Cordialement,
                L'équipe RH
                """,
                
                "other": """
                Bonjour,
                
                Nous avons bien reçu votre message concernant le concours de recrutement.
                Votre demande est en cours de traitement par notre équipe.
                
                Nous vous recontacterons dans les plus brefs délais avec une réponse personnalisée.
                
                Cordialement,
                L'équipe RH
                """
            }
            
            # Get base template
            response_text = templates.get(category, templates["other"])
            
            # Personalize with extracted information
            name = extracted_info.get("name", "")
            if name:
                response_text = f"Bonjour {name},\n" + response_text.split("Bonjour,", 1)[1]
            
            response_data = {
                "content": response_text.strip(),
                "category": category,
                "generated_at": datetime.now().isoformat(),
                "personalized": bool(name),
                "requires_review": classification["urgency"] == "high" or classification["confidence"] < 0.7
            }
            
            self.log_action("response_generated", {
                "category": category,
                "personalized": response_data["personalized"],
                "requires_review": response_data["requires_review"]
            })
            
            return response_data
            
        except Exception as e:
            self.logger.error(f"Error generating response: {str(e)}")
            return {
                "content": "Nous avons bien reçu votre message et vous recontacterons bientôt.",
                "category": "error",
                "generated_at": datetime.now().isoformat(),
                "personalized": False,
                "requires_review": True
            }

class AgentRHSupervisor(BaseAgent):
    """Agent responsible for HR supervision and validation"""
    
    def __init__(self):
        super().__init__("RHSupervisor")
    
    def requires_validation(self, classification: Dict, proposed_actions: List[Dict]) -> bool:
        """Determine if complaint requires HR validation"""
        # High urgency always requires validation
        if classification["urgency"] == "high":
            return True
        
        # Low confidence classifications require validation
        if classification["confidence"] < 0.7:
            return True
        
        # Database modification actions require validation
        for action in proposed_actions:
            if action.get("type") in ["delete_candidate", "update_candidate"]:
                return True
        
        return False
    
    def create_validation_request(self, complaint_id: str, classification: Dict, 
                                 proposed_actions: List[Dict], suggested_response: Dict) -> Dict:
        """Create validation request for HR review"""
        
        validation_request = {
            "complaint_id": complaint_id,
            "status": "pending_validation",
            "classification": classification,
            "proposed_actions": proposed_actions,
            "suggested_response": suggested_response,
            "created_at": datetime.now().isoformat(),
            "urgency": classification["urgency"],
            "confidence": classification["confidence"],
            "validation_reasons": []
        }
        
        # Add validation reasons
        if classification["urgency"] == "high":
            validation_request["validation_reasons"].append("High urgency complaint")
        
        if classification["confidence"] < 0.7:
            validation_request["validation_reasons"].append("Low confidence classification")
        
        if any(action.get("type") in ["delete_candidate", "update_candidate"] for action in proposed_actions):
            validation_request["validation_reasons"].append("Database modification required")
        
        self.log_action("validation_request_created", {
            "complaint_id": complaint_id,
            "reasons": validation_request["validation_reasons"]
        })
        
        return validation_request

class ComplaintProcessor:
    """Main orchestrator for the multi-agent system"""
    
    def __init__(self):
        self.mail_reader = AgentMailReader()
        self.classifier = AgentClassifier()
        self.db_handler = AgentDatabaseHandler()
        self.response_generator = AgentResponseGenerator()
        self.hr_supervisor = AgentRHSupervisor()
        self.logger = logging.getLogger("ComplaintProcessor")
    
    def process_complaint(self, email_content: str, sender_email: str, subject: str) -> str:
        """Process a complete complaint through the agent pipeline"""
        try:
            # Step 1: Read and parse email
            email_data = self.mail_reader.process_email(email_content, sender_email, subject)
            
            # Step 2: Classify complaint
            classification = self.classifier.classify_complaint(email_data)
            
            # Step 3: Determine required actions
            proposed_actions = self._determine_actions(classification)
            
            # Step 4: Generate response
            suggested_response = self.response_generator.generate_response(classification)
            
            # Step 5: Create complaint record
            complaint_data = {
                "email_data": email_data,
                "classification": classification,
                "proposed_actions": proposed_actions,
                "suggested_response": suggested_response,
                "processing_status": "processed"
            }
            
            complaint_id = db_manager.add_complaint(complaint_data)
            
            # Step 6: Check if HR validation required
            if self.hr_supervisor.requires_validation(classification, proposed_actions):
                validation_request = self.hr_supervisor.create_validation_request(
                    complaint_id, classification, proposed_actions, suggested_response
                )
                db_manager.update_complaint(complaint_id, {
                    "validation_request": validation_request,
                    "status": "pending_validation"
                })
            else:
                # Auto-execute safe actions
                self._execute_actions(complaint_id, proposed_actions)
                db_manager.update_complaint(complaint_id, {"status": "auto_resolved"})
            
            self.logger.info(f"Complaint {complaint_id} processed successfully")
            return complaint_id
            
        except Exception as e:
            self.logger.error(f"Error processing complaint: {str(e)}")
            raise
    
    def _determine_actions(self, classification: Dict) -> List[Dict]:
        """Determine what actions should be taken based on classification"""
        actions = []
        category = classification["category"]
        extracted_info = classification.get("extracted_info", {})
        
        if category == "cin_duplicate":
            cin = extracted_info.get("cin")
            if cin:
                actions.append({
                    "type": "find_candidate",
                    "data": {"cin": cin},
                    "description": f"Look up candidate with CIN {cin}"
                })
                actions.append({
                    "type": "delete_candidate", 
                    "data": {"cin": cin},
                    "description": f"Delete existing registration for CIN {cin}"
                })
        
        elif category == "info_modification":
            cin = extracted_info.get("cin")
            if cin:
                actions.append({
                    "type": "find_candidate",
                    "data": {"cin": cin},
                    "description": f"Look up candidate with CIN {cin}"
                })
                # Note: Specific updates would need to be extracted from the email
        
        # All categories get a response action
        actions.append({
            "type": "send_response",
            "data": {"method": "email"},
            "description": "Send automated response to candidate"
        })
        
        return actions
    
    def _execute_actions(self, complaint_id: str, actions: List[Dict]) -> List[Dict]:
        """Execute approved actions"""
        results = []
        
        for action in actions:
            if action["type"] in ["find_candidate", "delete_candidate", "update_candidate"]:
                result = self.db_handler.execute_action(action["type"], action["data"])
                results.append(result)
        
        # Update complaint with action results
        db_manager.update_complaint(complaint_id, {"action_results": results})
        return results
    
    def validate_and_execute(self, complaint_id: str, approved_actions: List[str], 
                           hr_comments: str = "") -> bool:
        """Execute HR-validated actions"""
        try:
            complaint = db_manager.get_complaint(complaint_id)
            if not complaint:
                return False
            
            proposed_actions = complaint.get("proposed_actions", [])
            actions_to_execute = [
                action for i, action in enumerate(proposed_actions) 
                if str(i) in approved_actions
            ]
            
            # Execute approved actions
            results = self._execute_actions(complaint_id, actions_to_execute)
            
            # Update status
            db_manager.update_complaint(complaint_id, {
                "status": "hr_validated",
                "hr_comments": hr_comments,
                "validated_at": datetime.now().isoformat(),
                "executed_actions": actions_to_execute
            })
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error validating complaint {complaint_id}: {str(e)}")
            return False

# Global processor instance
complaint_processor = ComplaintProcessor()
