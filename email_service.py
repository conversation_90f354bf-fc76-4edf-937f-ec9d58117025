"""
Email Service for reading and sending emails
Supports IMAP for reading and SMTP for sending
"""

import os
import email
import imaplib
import smtplib
import logging
from datetime import datetime
from typing import Dict, List, Optional, Tuple
from email.mime.text import MIMEText
from email.mime.multipart import MI<PERSON><PERSON>ultipart
from email.header import decode_header
from email_reply_parser import EmailReplyParser
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

logger = logging.getLogger(__name__)

class EmailService:
    """Service for reading emails from IMAP and sending via SMTP"""
    
    def __init__(self):
        # IMAP Configuration
        self.imap_host = os.getenv('EMAIL_HOST', 'imap.gmail.com')
        self.imap_port = int(os.getenv('EMAIL_PORT', 993))
        self.email_user = os.getenv('EMAIL_USER')
        self.email_password = os.getenv('EMAIL_PASSWORD')
        self.use_ssl = os.getenv('EMAIL_USE_SSL', 'true').lower() == 'true'
        
        # SMTP Configuration
        self.smtp_host = os.getenv('SMTP_HOST', 'smtp.gmail.com')
        self.smtp_port = int(os.getenv('SMTP_PORT', 587))
        self.smtp_user = os.getenv('SMTP_USER', self.email_user)
        self.smtp_password = os.getenv('SMTP_PASSWORD', self.email_password)
        self.smtp_use_tls = os.getenv('SMTP_USE_TLS', 'true').lower() == 'true'
        
        # Email processing configuration
        self.archive_folder = os.getenv('EMAIL_ARCHIVE_FOLDER', 'Processed')
        self.max_emails_per_batch = int(os.getenv('MAX_EMAILS_PER_BATCH', 10))
        
        logger.info("Email service initialized")
    
    def connect_imap(self) -> imaplib.IMAP4_SSL:
        """Connect to IMAP server"""
        try:
            if self.use_ssl:
                mail = imaplib.IMAP4_SSL(self.imap_host, self.imap_port)
            else:
                mail = imaplib.IMAP4(self.imap_host, self.imap_port)
            
            mail.login(self.email_user, self.email_password)
            logger.info("IMAP connection established")
            return mail
        except Exception as e:
            logger.error(f"IMAP connection failed: {str(e)}")
            raise
    
    def connect_smtp(self) -> smtplib.SMTP:
        """Connect to SMTP server"""
        try:
            smtp = smtplib.SMTP(self.smtp_host, self.smtp_port)
            if self.smtp_use_tls:
                smtp.starttls()
            smtp.login(self.smtp_user, self.smtp_password)
            logger.info("SMTP connection established")
            return smtp
        except Exception as e:
            logger.error(f"SMTP connection failed: {str(e)}")
            raise
    
    def decode_email_header(self, header: str) -> str:
        """Decode email header"""
        try:
            decoded_header = decode_header(header)
            decoded_string = ""
            for part, encoding in decoded_header:
                if isinstance(part, bytes):
                    decoded_string += part.decode(encoding or 'utf-8')
                else:
                    decoded_string += part
            return decoded_string
        except Exception as e:
            logger.warning(f"Header decode failed: {str(e)}")
            return header
    
    def extract_email_content(self, msg: email.message.Message) -> Dict:
        """Extract content from email message"""
        try:
            # Extract basic headers
            subject = self.decode_email_header(msg.get('Subject', ''))
            sender = self.decode_email_header(msg.get('From', ''))
            date = msg.get('Date', '')
            message_id = msg.get('Message-ID', '')
            
            # Extract body content
            body = ""
            html_body = ""
            
            if msg.is_multipart():
                for part in msg.walk():
                    content_type = part.get_content_type()
                    content_disposition = str(part.get('Content-Disposition', ''))
                    
                    # Skip attachments
                    if 'attachment' in content_disposition:
                        continue
                    
                    if content_type == 'text/plain':
                        body = part.get_payload(decode=True).decode('utf-8', errors='ignore')
                    elif content_type == 'text/html':
                        html_body = part.get_payload(decode=True).decode('utf-8', errors='ignore')
            else:
                content_type = msg.get_content_type()
                if content_type == 'text/plain':
                    body = msg.get_payload(decode=True).decode('utf-8', errors='ignore')
                elif content_type == 'text/html':
                    html_body = msg.get_payload(decode=True).decode('utf-8', errors='ignore')
            
            # Use plain text if available, otherwise try to extract from HTML
            if body:
                # Parse reply to get only the new content
                parsed_body = EmailReplyParser.parse_reply(body)
                content = parsed_body if parsed_body else body
            elif html_body:
                # Simple HTML to text conversion (basic)
                import re
                content = re.sub('<[^<]+?>', '', html_body)
                content = EmailReplyParser.parse_reply(content)
            else:
                content = ""
            
            return {
                'subject': subject,
                'sender': sender,
                'date': date,
                'message_id': message_id,
                'content': content.strip(),
                'raw_body': body,
                'html_body': html_body
            }
            
        except Exception as e:
            logger.error(f"Email content extraction failed: {str(e)}")
            return {
                'subject': 'Error extracting subject',
                'sender': 'Error extracting sender',
                'date': '',
                'message_id': '',
                'content': 'Error extracting content',
                'raw_body': '',
                'html_body': ''
            }
    
    def read_unread_emails(self, folder: str = 'INBOX') -> List[Dict]:
        """Read unread emails from specified folder"""
        try:
            mail = self.connect_imap()
            mail.select(folder)
            
            # Search for unread emails
            status, messages = mail.search(None, 'UNSEEN')
            
            if status != 'OK':
                logger.warning("No unread emails found")
                return []
            
            email_ids = messages[0].split()
            emails = []
            
            # Limit the number of emails processed per batch
            email_ids = email_ids[:self.max_emails_per_batch]
            
            for email_id in email_ids:
                try:
                    # Fetch email
                    status, msg_data = mail.fetch(email_id, '(RFC822)')
                    
                    if status != 'OK':
                        continue
                    
                    # Parse email
                    raw_email = msg_data[0][1]
                    msg = email.message_from_bytes(raw_email)
                    
                    # Extract content
                    email_content = self.extract_email_content(msg)
                    email_content['email_id'] = email_id.decode()
                    email_content['folder'] = folder
                    
                    emails.append(email_content)
                    
                    logger.info(f"Email processed: {email_content['subject']}")
                    
                except Exception as e:
                    logger.error(f"Error processing email {email_id}: {str(e)}")
                    continue
            
            mail.close()
            mail.logout()
            
            logger.info(f"Read {len(emails)} unread emails")
            return emails
            
        except Exception as e:
            logger.error(f"Error reading emails: {str(e)}")
            return []
    
    def mark_email_as_read(self, email_id: str, folder: str = 'INBOX'):
        """Mark email as read"""
        try:
            mail = self.connect_imap()
            mail.select(folder)
            mail.store(email_id, '+FLAGS', '\\Seen')
            mail.close()
            mail.logout()
            logger.info(f"Email {email_id} marked as read")
        except Exception as e:
            logger.error(f"Error marking email as read: {str(e)}")
    
    def move_email_to_folder(self, email_id: str, source_folder: str = 'INBOX', 
                           target_folder: str = None):
        """Move email to specified folder"""
        try:
            if not target_folder:
                target_folder = self.archive_folder
            
            mail = self.connect_imap()
            mail.select(source_folder)
            
            # Copy email to target folder
            mail.copy(email_id, target_folder)
            
            # Mark original as deleted
            mail.store(email_id, '+FLAGS', '\\Deleted')
            
            # Expunge to actually delete
            mail.expunge()
            
            mail.close()
            mail.logout()
            
            logger.info(f"Email {email_id} moved to {target_folder}")
            
        except Exception as e:
            logger.error(f"Error moving email: {str(e)}")
    
    def send_email(self, to_email: str, subject: str, body: str, 
                   reply_to_message_id: str = None) -> bool:
        """Send email response"""
        try:
            smtp = self.connect_smtp()
            
            # Create message
            msg = MIMEMultipart()
            msg['From'] = self.smtp_user
            msg['To'] = to_email
            msg['Subject'] = subject
            
            # Add reply-to header if this is a reply
            if reply_to_message_id:
                msg['In-Reply-To'] = reply_to_message_id
                msg['References'] = reply_to_message_id
            
            # Add body
            msg.attach(MIMEText(body, 'plain', 'utf-8'))
            
            # Send email
            smtp.send_message(msg)
            smtp.quit()
            
            logger.info(f"Email sent to {to_email}")
            return True
            
        except Exception as e:
            logger.error(f"Error sending email: {str(e)}")
            return False
    
    def send_response_email(self, original_email: Dict, response_content: str) -> bool:
        """Send response to original email"""
        try:
            # Extract sender email from the original email
            sender = original_email['sender']
            # Simple email extraction (you might want to use a more robust parser)
            import re
            email_match = re.search(r'<(.+?)>', sender)
            if email_match:
                to_email = email_match.group(1)
            else:
                # If no angle brackets, assume the whole string is the email
                to_email = sender.strip()
            
            # Create reply subject
            original_subject = original_email['subject']
            if not original_subject.lower().startswith('re:'):
                reply_subject = f"Re: {original_subject}"
            else:
                reply_subject = original_subject
            
            # Send the response
            return self.send_email(
                to_email=to_email,
                subject=reply_subject,
                body=response_content,
                reply_to_message_id=original_email.get('message_id')
            )
            
        except Exception as e:
            logger.error(f"Error sending response email: {str(e)}")
            return False

# Global email service instance
email_service = EmailService()
