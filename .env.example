# AI Team Orchestrator Configuration
# Copy this file to .env and fill in your actual values

# Database Configuration (Choose one)
DATABASE_TYPE=mysql  # mysql or oracle
MYSQL_HOST=localhost
MYSQL_PORT=3306
MYSQL_DATABASE=hr_complaints
MYSQL_USER=root
MYSQL_PASSWORD=your_password

# Oracle Configuration (if using Oracle)
ORACLE_HOST=localhost
ORACLE_PORT=1521
ORACLE_SERVICE_NAME=XE
ORACLE_USER=hr_user
ORACLE_PASSWORD=your_password

# AI Configuration
GROQ_API_KEY=your_groq_api_key_here
OPENAI_API_KEY=your_openai_api_key_here  # Optional fallback

# Email Configuration
EMAIL_HOST=imap.gmail.com
EMAIL_PORT=993
EMAIL_USER=<EMAIL>
EMAIL_PASSWORD=your_email_password
EMAIL_USE_SSL=true

# SMTP Configuration for sending responses
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your_smtp_password
SMTP_USE_TLS=true

# Flask Configuration
FLASK_SECRET_KEY=your-secret-key-change-in-production
FLASK_DEBUG=true
FLASK_HOST=0.0.0.0
FLASK_PORT=5000

# Logging Configuration
LOG_LEVEL=INFO
LOG_FILE=logs/app.log

# Agent Configuration
AUTO_EXECUTE_SAFE_ACTIONS=true
REQUIRE_HR_VALIDATION_THRESHOLD=0.7
HIGH_URGENCY_NOTIFICATION=true

# Email Processing Configuration
EMAIL_CHECK_INTERVAL=300  # seconds
MAX_EMAILS_PER_BATCH=10
EMAIL_ARCHIVE_FOLDER=Processed

# Response Templates Language
RESPONSE_LANGUAGE=fr  # fr for French, en for English
