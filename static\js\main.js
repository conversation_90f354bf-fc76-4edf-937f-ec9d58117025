// Main JavaScript for AI Complaint Management System
document.addEventListener('DOMContentLoaded', function() {
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Auto-refresh complaints every 30 seconds on dashboard
    if (window.location.pathname === '/') {
        setInterval(checkForUpdates, 30000);
    }

    // Form validation
    const complaintForm = document.querySelector('form[action*="submit_complaint"]');
    if (complaintForm) {
        complaintForm.addEventListener('submit', function(e) {
            const emailContent = document.getElementById('email_content').value.trim();
            const senderEmail = document.getElementById('sender_email').value.trim();
            
            if (!emailContent) {
                e.preventDefault();
                showAlert('Please enter the email content', 'error');
                return;
            }
            
            if (!senderEmail) {
                e.preventDefault();
                showAlert('Please enter the sender email', 'error');
                return;
            }
            
            // Show processing indicator
            const submitBtn = this.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Processing...';
            submitBtn.disabled = true;
            
            // Re-enable after 10 seconds as fallback
            setTimeout(() => {
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
            }, 10000);
        });
    }

    // Validation form handling
    const validationForm = document.querySelector('form[action*="validate_complaint"]');
    if (validationForm) {
        validationForm.addEventListener('submit', function(e) {
            const checkedActions = this.querySelectorAll('input[name="approved_actions"]:checked');
            if (checkedActions.length === 0) {
                e.preventDefault();
                showAlert('Please select at least one action to approve, or reject the complaint', 'warning');
                return;
            }
            
            // Confirm validation
            if (!confirm('Are you sure you want to execute the selected actions? This action cannot be undone.')) {
                e.preventDefault();
                return;
            }
        });
    }

    // Action selection helpers
    const actionCheckboxes = document.querySelectorAll('input[name="approved_actions"]');
    if (actionCheckboxes.length > 0) {
        // Add select all/none buttons
        addActionSelectors();
    }
});

function refreshComplaints() {
    const refreshBtn = document.querySelector('button[onclick="refreshComplaints()"]');
    if (refreshBtn) {
        const originalHTML = refreshBtn.innerHTML;
        refreshBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Refreshing...';
        refreshBtn.disabled = true;
        
        // Reload the page
        setTimeout(() => {
            window.location.reload();
        }, 500);
    }
}

function checkForUpdates() {
    // Fetch latest complaints count via API
    fetch('/api/complaints')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const currentCount = document.querySelectorAll('tbody tr').length;
                const newCount = data.complaints.length;
                
                if (newCount !== currentCount) {
                    // Show notification of new complaints
                    showAlert(`${newCount - currentCount} new complaint(s) received`, 'info');
                    
                    // Optionally auto-refresh
                    setTimeout(() => {
                        window.location.reload();
                    }, 2000);
                }
            }
        })
        .catch(error => {
            console.log('Auto-refresh check failed:', error);
        });
}

function showAlert(message, type = 'info') {
    // Create alert element
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    // Insert at top of container
    const container = document.querySelector('.container');
    const firstChild = container.firstElementChild;
    container.insertBefore(alertDiv, firstChild);
    
    // Auto-dismiss after 5 seconds
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}

function addActionSelectors() {
    const checkboxContainer = document.querySelector('form[action*="validate_complaint"]');
    if (!checkboxContainer) return;
    
    // Create select all/none buttons
    const buttonGroup = document.createElement('div');
    buttonGroup.className = 'mb-3';
    buttonGroup.innerHTML = `
        <div class="btn-group btn-group-sm" role="group">
            <button type="button" class="btn btn-outline-secondary" onclick="selectAllActions()">
                <i class="fas fa-check-double me-1"></i>Select All
            </button>
            <button type="button" class="btn btn-outline-secondary" onclick="selectNoActions()">
                <i class="fas fa-times me-1"></i>Select None
            </button>
        </div>
    `;
    
    // Insert before the first checkbox
    const firstCheckbox = checkboxContainer.querySelector('.form-check');
    if (firstCheckbox) {
        checkboxContainer.insertBefore(buttonGroup, firstCheckbox);
    }
}

function selectAllActions() {
    const checkboxes = document.querySelectorAll('input[name="approved_actions"]');
    checkboxes.forEach(checkbox => {
        checkbox.checked = true;
    });
}

function selectNoActions() {
    const checkboxes = document.querySelectorAll('input[name="approved_actions"]');
    checkboxes.forEach(checkbox => {
        checkbox.checked = false;
    });
}

// Real-time status updates for complaint detail page
if (window.location.pathname.includes('/complaint/')) {
    setInterval(checkComplaintStatus, 10000);
}

function checkComplaintStatus() {
    const complaintId = window.location.pathname.split('/').pop();
    if (!complaintId) return;
    
    fetch(`/api/complaint/${complaintId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const currentStatus = document.querySelector('.card-header .badge').textContent.trim();
                const newStatus = data.complaint.status.replace('_', ' ').split(' ').map(word => 
                    word.charAt(0).toUpperCase() + word.slice(1)
                ).join(' ');
                
                if (currentStatus !== newStatus) {
                    // Status changed, reload page
                    window.location.reload();
                }
            }
        })
        .catch(error => {
            console.log('Status check failed:', error);
        });
}

// Keyboard shortcuts
document.addEventListener('keydown', function(e) {
    // Ctrl+R to refresh complaints
    if (e.ctrlKey && e.key === 'r' && window.location.pathname === '/') {
        e.preventDefault();
        refreshComplaints();
    }
    
    // Escape to go back to dashboard
    if (e.key === 'Escape' && window.location.pathname !== '/') {
        window.location.href = '/';
    }
});

// Copy complaint ID to clipboard
function copyComplaintId(complaintId) {
    navigator.clipboard.writeText(complaintId).then(function() {
        showAlert('Complaint ID copied to clipboard', 'success');
    });
}

// Add copy buttons to complaint IDs
document.addEventListener('DOMContentLoaded', function() {
    const complaintCodes = document.querySelectorAll('code');
    complaintCodes.forEach(code => {
        if (code.textContent.startsWith('COMP_')) {
            code.style.cursor = 'pointer';
            code.title = 'Click to copy';
            code.addEventListener('click', function() {
                copyComplaintId(this.textContent);
            });
        }
    });
});

// Enhanced form validation with real-time feedback
document.addEventListener('DOMContentLoaded', function() {
    const emailInput = document.getElementById('sender_email');
    const contentInput = document.getElementById('email_content');
    
    if (emailInput) {
        emailInput.addEventListener('blur', function() {
            validateEmail(this);
        });
    }
    
    if (contentInput) {
        contentInput.addEventListener('input', function() {
            updateCharacterCount(this);
        });
    }
});

function validateEmail(input) {
    const email = input.value.trim();
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    
    if (email && !emailRegex.test(email)) {
        input.classList.add('is-invalid');
        showFieldError(input, 'Please enter a valid email address');
    } else {
        input.classList.remove('is-invalid');
        hideFieldError(input);
    }
}

function updateCharacterCount(input) {
    const maxLength = 2000;
    const currentLength = input.value.length;
    
    let counter = input.parentNode.querySelector('.character-count');
    if (!counter) {
        counter = document.createElement('small');
        counter.className = 'character-count text-muted';
        input.parentNode.appendChild(counter);
    }
    
    counter.textContent = `${currentLength}/${maxLength} characters`;
    
    if (currentLength > maxLength) {
        counter.classList.add('text-danger');
        counter.classList.remove('text-muted');
        input.classList.add('is-invalid');
    } else {
        counter.classList.remove('text-danger');
        counter.classList.add('text-muted');
        input.classList.remove('is-invalid');
    }
}

function showFieldError(input, message) {
    let errorDiv = input.parentNode.querySelector('.invalid-feedback');
    if (!errorDiv) {
        errorDiv = document.createElement('div');
        errorDiv.className = 'invalid-feedback';
        input.parentNode.appendChild(errorDiv);
    }
    errorDiv.textContent = message;
}

function hideFieldError(input) {
    const errorDiv = input.parentNode.querySelector('.invalid-feedback');
    if (errorDiv) {
        errorDiv.remove();
    }
}
