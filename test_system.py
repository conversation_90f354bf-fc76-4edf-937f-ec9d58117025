#!/usr/bin/env python3
"""
Test script for AI Team Orchestrator
Tests various components and workflows
"""

import os
import sys
import json
import logging
from datetime import datetime
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_database_connection():
    """Test database connection and basic operations"""
    print("🔍 Testing Database Connection...")
    try:
        from database import db_manager
        
        # Test connection
        candidates = db_manager.get_all_candidates()
        print(f"✅ Database connected - Found {len(candidates)} candidates")
        
        # Test candidate operations
        test_candidate = db_manager.find_candidate_by_cin("AB123456")
        if test_candidate:
            print(f"✅ Sample candidate found: {test_candidate['name']}")
        else:
            print("⚠️  No sample candidate found")
        
        return True
    except Exception as e:
        print(f"❌ Database test failed: {str(e)}")
        return False

def test_agents():
    """Test individual agents"""
    print("\n🤖 Testing Individual Agents...")
    
    try:
        from agents import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, AgentClassifier, AgentResponseGenerator
        
        # Test email reading
        mail_reader = AgentMailReader()
        email_data = mail_reader.process_email(
            "Bonjour, j'ai un problème avec mon CIN AB123456 qui est déjà utilisé.",
            "<EMAIL>",
            "Problème inscription"
        )
        print("✅ AgentMailReader working")
        
        # Test classification
        classifier = AgentClassifier()
        classification = classifier.classify_complaint(email_data)
        print(f"✅ AgentClassifier working - Category: {classification['category']}")
        
        # Test response generation
        response_gen = AgentResponseGenerator()
        response = response_gen.generate_response(classification)
        print("✅ AgentResponseGenerator working")
        
        return True
    except Exception as e:
        print(f"❌ Agent test failed: {str(e)}")
        return False

def test_langgraph_orchestrator():
    """Test LangGraph orchestrator"""
    print("\n🔄 Testing LangGraph Orchestrator...")
    
    try:
        from langgraph_orchestrator import langgraph_orchestrator
        
        # Test complaint processing
        complaint_id = langgraph_orchestrator.process_complaint(
            email_content="Bonjour, j'ai un problème avec mon inscription. Mon CIN AB123456 semble déjà utilisé mais je n'ai jamais fait d'inscription avant. Pouvez-vous m'aider à résoudre ce problème ? Merci.",
            sender_email="<EMAIL>",
            subject="Problème CIN déjà utilisé"
        )
        
        if complaint_id and complaint_id != "ERROR":
            print(f"✅ LangGraph orchestrator working - Complaint ID: {complaint_id}")
            return True
        else:
            print("❌ LangGraph orchestrator failed")
            return False
            
    except Exception as e:
        print(f"❌ LangGraph test failed: {str(e)}")
        return False

def test_email_service():
    """Test email service (if configured)"""
    print("\n📧 Testing Email Service...")
    
    email_user = os.getenv('EMAIL_USER')
    email_password = os.getenv('EMAIL_PASSWORD')
    
    if not email_user or not email_password:
        print("⚠️  Email service not configured - skipping test")
        return True
    
    try:
        from email_service import email_service
        
        # Test IMAP connection (without actually reading emails)
        print("✅ Email service configuration loaded")
        print(f"   IMAP Host: {email_service.imap_host}")
        print(f"   SMTP Host: {email_service.smtp_host}")
        
        # Note: We don't actually connect to avoid consuming real emails
        return True
    except Exception as e:
        print(f"❌ Email service test failed: {str(e)}")
        return False

def test_complaint_workflow():
    """Test complete complaint workflow"""
    print("\n🔄 Testing Complete Workflow...")
    
    try:
        # Test data
        test_complaints = [
            {
                "content": "Bonjour, mon CIN AB123456 est déjà utilisé mais je n'ai jamais fait d'inscription. Aidez-moi svp.",
                "sender": "<EMAIL>",
                "subject": "CIN déjà utilisé",
                "expected_category": "cin_duplicate"
            },
            {
                "content": "Bonjour, je n'arrive pas à imprimer ma fiche de candidature. Le bouton ne fonctionne pas.",
                "sender": "<EMAIL>", 
                "subject": "Problème impression",
                "expected_category": "print_issue"
            },
            {
                "content": "Bonjour, j'ai fait une erreur dans mon nom lors de l'inscription. Comment puis-je le corriger ?",
                "sender": "<EMAIL>",
                "subject": "Correction nom",
                "expected_category": "info_modification"
            }
        ]
        
        from langgraph_orchestrator import langgraph_orchestrator
        from database import db_manager
        
        results = []
        
        for i, test_case in enumerate(test_complaints, 1):
            print(f"\n   Test {i}: {test_case['subject']}")
            
            try:
                complaint_id = langgraph_orchestrator.process_complaint(
                    test_case["content"],
                    test_case["sender"],
                    test_case["subject"]
                )
                
                if complaint_id and complaint_id != "ERROR":
                    # Verify complaint was saved
                    complaint = db_manager.get_complaint(complaint_id)
                    if complaint:
                        category = complaint['classification']['category']
                        print(f"   ✅ Processed as {category} (ID: {complaint_id})")
                        results.append(True)
                    else:
                        print(f"   ❌ Complaint not found in database")
                        results.append(False)
                else:
                    print(f"   ❌ Processing failed")
                    results.append(False)
                    
            except Exception as e:
                print(f"   ❌ Error: {str(e)}")
                results.append(False)
        
        success_rate = sum(results) / len(results) * 100
        print(f"\n📊 Workflow Test Results: {success_rate:.1f}% success rate")
        
        return success_rate >= 80  # 80% success rate threshold
        
    except Exception as e:
        print(f"❌ Workflow test failed: {str(e)}")
        return False

def run_all_tests():
    """Run all tests"""
    print("🧪 AI Team Orchestrator - System Tests")
    print("=" * 50)
    
    tests = [
        ("Database Connection", test_database_connection),
        ("Individual Agents", test_agents),
        ("LangGraph Orchestrator", test_langgraph_orchestrator),
        ("Email Service", test_email_service),
        ("Complete Workflow", test_complaint_workflow)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} test crashed: {str(e)}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 Test Summary:")
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"   {status} - {test_name}")
        if result:
            passed += 1
    
    success_rate = passed / len(results) * 100
    print(f"\n🎯 Overall Success Rate: {success_rate:.1f}% ({passed}/{len(results)})")
    
    if success_rate >= 80:
        print("🎉 System is ready for use!")
    else:
        print("⚠️  System needs attention before production use")
    
    return success_rate >= 80

def main():
    """Main entry point"""
    if len(sys.argv) > 1:
        test_name = sys.argv[1].lower()
        
        if test_name == 'db':
            test_database_connection()
        elif test_name == 'agents':
            test_agents()
        elif test_name == 'langgraph':
            test_langgraph_orchestrator()
        elif test_name == 'email':
            test_email_service()
        elif test_name == 'workflow':
            test_complaint_workflow()
        elif test_name == 'all':
            run_all_tests()
        else:
            print("Usage: python test_system.py [db|agents|langgraph|email|workflow|all]")
    else:
        run_all_tests()

if __name__ == "__main__":
    main()
