#!/usr/bin/env python3
"""
Startup script for AI Team Orchestrator
Provides easy setup and configuration options
"""

import os
import sys
import logging
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def setup_logging():
    """Setup logging configuration"""
    log_dir = "logs"
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('logs/app.log'),
            logging.StreamHandler(sys.stdout)
        ]
    )

def check_environment():
    """Check if required environment variables are set"""
    required_vars = [
        'GROQ_API_KEY',
        'DATABASE_TYPE',
    ]
    
    missing_vars = []
    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)
    
    if missing_vars:
        print("❌ Missing required environment variables:")
        for var in missing_vars:
            print(f"   - {var}")
        print("\n📝 Please copy .env.example to .env and configure the missing variables")
        return False
    
    print("✅ Environment variables configured")
    return True

def check_database():
    """Check database connection"""
    try:
        from database import db_manager
        print("✅ Database connection successful")
        return True
    except Exception as e:
        print(f"❌ Database connection failed: {str(e)}")
        print("💡 Please check your database configuration in .env")
        return False

def start_application():
    """Start the Flask application"""
    try:
        from app import app
        from email_monitor import email_monitor
        
        print("🚀 Starting AI Team Orchestrator...")
        print("📊 Dashboard: http://localhost:5000")
        print("📧 Email Monitor: http://localhost:5000/email_monitor")
        print("🛑 Press Ctrl+C to stop")
        
        # Start email monitoring if configured
        if os.getenv('EMAIL_USER') and os.getenv('EMAIL_PASSWORD'):
            print("📬 Starting email monitoring...")
            email_monitor.start_monitoring()
        else:
            print("⚠️  Email monitoring disabled (no email credentials)")
        
        # Start Flask app
        app.run(
            host=os.getenv('FLASK_HOST', '0.0.0.0'),
            port=int(os.getenv('FLASK_PORT', 5000)),
            debug=os.getenv('FLASK_DEBUG', 'true').lower() == 'true'
        )
        
    except KeyboardInterrupt:
        print("\n🛑 Shutting down...")
        try:
            email_monitor.stop_monitoring()
        except:
            pass
        print("👋 Goodbye!")
    except Exception as e:
        print(f"❌ Application startup failed: {str(e)}")
        sys.exit(1)

def show_help():
    """Show help information"""
    print("""
🤖 AI Team Orchestrator - Multi-Agent Complaint Management System

Usage:
    python start.py [command]

Commands:
    start       Start the application (default)
    check       Check configuration and dependencies
    setup       Interactive setup wizard
    help        Show this help message

Examples:
    python start.py              # Start the application
    python start.py check        # Check configuration
    python start.py setup        # Run setup wizard
    
Environment:
    Copy .env.example to .env and configure your settings
    
Required:
    - GROQ_API_KEY: Your Groq API key
    - DATABASE_TYPE: mysql or oracle
    - Database credentials (MYSQL_* or ORACLE_*)
    
Optional:
    - Email credentials for automatic monitoring
    - SMTP credentials for sending responses
    
For more information, see README.md
""")

def interactive_setup():
    """Interactive setup wizard"""
    print("🔧 AI Team Orchestrator Setup Wizard")
    print("=" * 50)
    
    # Check if .env exists
    if not os.path.exists('.env'):
        print("📝 Creating .env file from template...")
        if os.path.exists('.env.example'):
            import shutil
            shutil.copy('.env.example', '.env')
            print("✅ .env file created")
        else:
            print("❌ .env.example not found")
            return
    
    print("\n🔑 Please configure the following in your .env file:")
    print("1. GROQ_API_KEY - Get from https://console.groq.com/")
    print("2. Database settings (MySQL or Oracle)")
    print("3. Email settings (optional, for automatic monitoring)")
    
    print("\n📖 See README.md for detailed configuration instructions")
    print("🚀 Run 'python start.py check' to verify your configuration")

def check_configuration():
    """Check system configuration"""
    print("🔍 Checking AI Team Orchestrator Configuration")
    print("=" * 50)
    
    # Check environment
    env_ok = check_environment()
    
    # Check database
    db_ok = check_database() if env_ok else False
    
    # Check email configuration
    email_configured = bool(os.getenv('EMAIL_USER') and os.getenv('EMAIL_PASSWORD'))
    smtp_configured = bool(os.getenv('SMTP_USER') and os.getenv('SMTP_PASSWORD'))
    
    print(f"\n📧 Email Configuration:")
    print(f"   IMAP: {'✅ Configured' if email_configured else '⚠️  Not configured'}")
    print(f"   SMTP: {'✅ Configured' if smtp_configured else '⚠️  Not configured'}")
    
    # Check AI API
    groq_key = os.getenv('GROQ_API_KEY')
    print(f"\n🤖 AI Configuration:")
    print(f"   Groq API: {'✅ Configured' if groq_key else '❌ Missing'}")
    
    # Summary
    print(f"\n📊 Summary:")
    if env_ok and db_ok:
        print("✅ System ready to start!")
        if not email_configured:
            print("💡 Configure email settings for automatic monitoring")
    else:
        print("❌ Configuration incomplete")
        print("💡 Run 'python start.py setup' for help")

def main():
    """Main entry point"""
    setup_logging()
    
    # Parse command line arguments
    command = sys.argv[1] if len(sys.argv) > 1 else 'start'
    
    if command == 'help':
        show_help()
    elif command == 'setup':
        interactive_setup()
    elif command == 'check':
        check_configuration()
    elif command == 'start':
        if check_environment() and check_database():
            start_application()
        else:
            print("❌ Cannot start application due to configuration errors")
            print("💡 Run 'python start.py setup' for help")
            sys.exit(1)
    else:
        print(f"❌ Unknown command: {command}")
        print("💡 Run 'python start.py help' for usage information")
        sys.exit(1)

if __name__ == "__main__":
    main()
