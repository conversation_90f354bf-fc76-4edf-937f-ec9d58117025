"""
Email Monitor Service
Automatically monitors email inbox for new complaints and processes them
"""

import os
import time
import logging
import schedule
import threading
from datetime import datetime
from typing import Dict, List
from dotenv import load_dotenv

from email_service import email_service
from langgraph_orchestrator import langgraph_orchestrator
from database import db_manager

# Load environment variables
load_dotenv()

logger = logging.getLogger(__name__)

class EmailMonitor:
    """Service to monitor emails and automatically process complaints"""
    
    def __init__(self):
        self.check_interval = int(os.getenv('EMAIL_CHECK_INTERVAL', 300))  # seconds
        self.is_running = False
        self.monitor_thread = None
        self.auto_execute = os.getenv('AUTO_EXECUTE_SAFE_ACTIONS', 'true').lower() == 'true'
        
        # Keywords to identify complaint emails
        self.complaint_keywords = [
            'réclamation', 'plainte', 'problème', 'erreur', 'bug',
            'inscription', 'candidature', 'concours', 'cin',
            'imprimer', 'télécharger', 'connexion', 'mot de passe',
            'complaint', 'problem', 'issue', 'error', 'help'
        ]
        
        logger.info(f"Email monitor initialized with {self.check_interval}s interval")
    
    def is_complaint_email(self, email_data: Dict) -> bool:
        """Determine if an email is a complaint based on content and subject"""
        try:
            subject = email_data.get('subject', '').lower()
            content = email_data.get('content', '').lower()
            
            # Check for complaint keywords
            text_to_check = f"{subject} {content}"
            
            for keyword in self.complaint_keywords:
                if keyword in text_to_check:
                    return True
            
            # Additional heuristics
            # Check if email mentions specific issues
            issue_indicators = [
                'ne fonctionne pas', 'ne marche pas', 'problème avec',
                'erreur', 'impossible de', 'ne peut pas',
                'doesn\'t work', 'not working', 'can\'t', 'unable to'
            ]
            
            for indicator in issue_indicators:
                if indicator in text_to_check:
                    return True
            
            # Check if email is asking for help or support
            help_indicators = [
                'aide', 'aidez-moi', 'support', 'assistance',
                'help', 'please help', 'need help'
            ]
            
            for indicator in help_indicators:
                if indicator in text_to_check:
                    return True
            
            return False
            
        except Exception as e:
            logger.error(f"Error checking if email is complaint: {str(e)}")
            return False
    
    def process_new_emails(self) -> List[str]:
        """Check for new emails and process complaints"""
        try:
            logger.info("Checking for new emails...")
            
            # Read unread emails
            emails = email_service.read_unread_emails()
            
            if not emails:
                logger.info("No new emails found")
                return []
            
            processed_complaints = []
            
            for email_data in emails:
                try:
                    # Check if this is a complaint email
                    if not self.is_complaint_email(email_data):
                        logger.info(f"Email '{email_data['subject']}' not identified as complaint")
                        # Mark as read but don't process
                        email_service.mark_email_as_read(email_data['email_id'])
                        continue
                    
                    logger.info(f"Processing complaint email: {email_data['subject']}")
                    
                    # Extract sender email
                    sender = email_data['sender']
                    import re
                    email_match = re.search(r'<(.+?)>', sender)
                    if email_match:
                        sender_email = email_match.group(1)
                    else:
                        sender_email = sender.strip()
                    
                    # Process through LangGraph orchestrator
                    complaint_id = langgraph_orchestrator.process_complaint(
                        email_content=email_data['content'],
                        sender_email=sender_email,
                        subject=email_data['subject']
                    )
                    
                    if complaint_id and complaint_id != "ERROR":
                        processed_complaints.append(complaint_id)
                        
                        # Mark email as read and optionally move to processed folder
                        email_service.mark_email_as_read(email_data['email_id'])
                        
                        # Move to processed folder if configured
                        if os.getenv('EMAIL_ARCHIVE_FOLDER'):
                            email_service.move_email_to_folder(
                                email_data['email_id'],
                                target_folder=os.getenv('EMAIL_ARCHIVE_FOLDER')
                            )
                        
                        # Send automatic response if complaint was auto-resolved
                        complaint = db_manager.get_complaint(complaint_id)
                        if complaint and complaint.get('status') == 'auto_resolved':
                            self._send_automatic_response(email_data, complaint)
                        
                        logger.info(f"Complaint {complaint_id} processed successfully")
                    else:
                        logger.error(f"Failed to process complaint from {sender_email}")
                
                except Exception as e:
                    logger.error(f"Error processing email: {str(e)}")
                    continue
            
            if processed_complaints:
                logger.info(f"Processed {len(processed_complaints)} complaints: {processed_complaints}")
            
            return processed_complaints
            
        except Exception as e:
            logger.error(f"Error in email processing: {str(e)}")
            return []
    
    def _send_automatic_response(self, original_email: Dict, complaint: Dict):
        """Send automatic response for auto-resolved complaints"""
        try:
            if not self.auto_execute:
                logger.info("Automatic responses disabled")
                return
            
            suggested_response = complaint.get('suggested_response', {})
            response_content = suggested_response.get('content', '')
            
            if response_content:
                success = email_service.send_response_email(original_email, response_content)
                if success:
                    # Update complaint to mark response as sent
                    db_manager.update_complaint(complaint['id'], {
                        'status': 'auto_resolved_with_response',
                        'response_sent_at': datetime.now().isoformat()
                    })
                    logger.info(f"Automatic response sent for complaint {complaint['id']}")
                else:
                    logger.error(f"Failed to send automatic response for complaint {complaint['id']}")
            
        except Exception as e:
            logger.error(f"Error sending automatic response: {str(e)}")
    
    def start_monitoring(self):
        """Start the email monitoring service"""
        if self.is_running:
            logger.warning("Email monitor is already running")
            return
        
        self.is_running = True
        
        # Schedule email checking
        schedule.every(self.check_interval).seconds.do(self.process_new_emails)
        
        # Start monitoring thread
        self.monitor_thread = threading.Thread(target=self._run_scheduler, daemon=True)
        self.monitor_thread.start()
        
        logger.info(f"Email monitoring started (checking every {self.check_interval} seconds)")
    
    def stop_monitoring(self):
        """Stop the email monitoring service"""
        self.is_running = False
        schedule.clear()
        
        if self.monitor_thread:
            self.monitor_thread.join(timeout=5)
        
        logger.info("Email monitoring stopped")
    
    def _run_scheduler(self):
        """Run the scheduler in a separate thread"""
        while self.is_running:
            try:
                schedule.run_pending()
                time.sleep(1)
            except Exception as e:
                logger.error(f"Scheduler error: {str(e)}")
                time.sleep(5)
    
    def check_now(self) -> List[str]:
        """Manually trigger email check"""
        logger.info("Manual email check triggered")
        return self.process_new_emails()
    
    def get_status(self) -> Dict:
        """Get monitoring status"""
        return {
            'is_running': self.is_running,
            'check_interval': self.check_interval,
            'auto_execute': self.auto_execute,
            'next_check': schedule.next_run() if schedule.jobs else None
        }

# Global email monitor instance
email_monitor = EmailMonitor()

# CLI functions for testing
def start_monitor():
    """Start email monitoring"""
    email_monitor.start_monitoring()

def stop_monitor():
    """Stop email monitoring"""
    email_monitor.stop_monitoring()

def check_emails():
    """Check emails now"""
    return email_monitor.check_now()

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1:
        command = sys.argv[1].lower()
        
        if command == 'start':
            start_monitor()
            try:
                while True:
                    time.sleep(1)
            except KeyboardInterrupt:
                stop_monitor()
        
        elif command == 'check':
            complaints = check_emails()
            print(f"Processed complaints: {complaints}")
        
        elif command == 'status':
            status = email_monitor.get_status()
            print(f"Monitor status: {status}")
        
        else:
            print("Usage: python email_monitor.py [start|check|status]")
    else:
        print("Usage: python email_monitor.py [start|check|status]")
