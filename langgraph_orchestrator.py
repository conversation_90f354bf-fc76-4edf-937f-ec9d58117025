"""
LangGraph-based Multi-Agent Orchestrator for HR Complaint Management
This module implements the agent workflow using LangGraph for better state management
and agent coordination.
"""

import json
import logging
from typing import Dict, List, Any, Optional, TypedDict
from datetime import datetime
from dotenv import load_dotenv

# Try to import LangGraph, fallback to basic orchestration if not available
try:
    from langgraph.graph import StateGraph, END
    from langchain.schema import BaseMessage, HumanMessage, AIMessage
    from langchain_community.chat_models import ChatGroq
    LANGGRAPH_AVAILABLE = True
except ImportError:
    print("⚠️  LangGraph not available, using basic orchestration")
    LANGGRAPH_AVAILABLE = False
    # Create dummy classes for compatibility
    class StateGraph:
        def __init__(self, state_type): pass
        def add_node(self, name, func): pass
        def add_edge(self, from_node, to_node): pass
        def add_conditional_edges(self, node, condition, mapping): pass
        def set_entry_point(self, node): pass
        def compile(self): return BasicOrchestrator()

    class BasicOrchestrator:
        def invoke(self, state):
            # Basic sequential processing without LangGraph
            try:
                from agents import complaint_processor
                complaint_id = complaint_processor.process_complaint(
                    state["email_content"],
                    state["sender_email"],
                    state["subject"]
                )
                # Return state-like object for compatibility
                state["complaint_id"] = complaint_id
                state["status"] = "completed"
                return state
            except Exception as e:
                state["error_message"] = str(e)
                state["status"] = "error"
                return state

    END = "END"

from agents import (
    AgentMailReader, AgentClassifier, AgentDatabaseHandler, 
    AgentResponseGenerator, AgentRHSupervisor
)
from database import db_manager

# Load environment variables
load_dotenv()

logger = logging.getLogger(__name__)

class ComplaintState(TypedDict):
    """State object for the complaint processing workflow"""
    complaint_id: Optional[str]
    email_content: str
    sender_email: str
    subject: str
    email_data: Optional[Dict]
    classification: Optional[Dict]
    proposed_actions: Optional[List[Dict]]
    suggested_response: Optional[Dict]
    validation_request: Optional[Dict]
    requires_validation: bool
    status: str
    action_results: Optional[List[Dict]]
    hr_comments: Optional[str]
    processing_log: List[str]
    error_message: Optional[str]

class LangGraphComplaintOrchestrator:
    """Multi-agent orchestrator for complaint processing (with or without LangGraph)"""

    def __init__(self):
        self.mail_reader = AgentMailReader()
        self.classifier = AgentClassifier()
        self.db_handler = AgentDatabaseHandler()
        self.response_generator = AgentResponseGenerator()
        self.hr_supervisor = AgentRHSupervisor()

        # Initialize workflow (LangGraph or basic)
        if LANGGRAPH_AVAILABLE:
            self.workflow = self._create_workflow()
            self.app = self.workflow.compile()
            logger.info("LangGraph Complaint Orchestrator initialized")
        else:
            self.app = None
            logger.info("Basic Complaint Orchestrator initialized (no LangGraph)")
    
    def _create_workflow(self) -> StateGraph:
        """Create the LangGraph workflow for complaint processing"""
        
        workflow = StateGraph(ComplaintState)
        
        # Add nodes for each agent
        workflow.add_node("read_email", self._read_email_node)
        workflow.add_node("classify_complaint", self._classify_complaint_node)
        workflow.add_node("determine_actions", self._determine_actions_node)
        workflow.add_node("generate_response", self._generate_response_node)
        workflow.add_node("check_validation", self._check_validation_node)
        workflow.add_node("execute_actions", self._execute_actions_node)
        workflow.add_node("create_validation_request", self._create_validation_request_node)
        workflow.add_node("save_complaint", self._save_complaint_node)
        workflow.add_node("handle_error", self._handle_error_node)
        
        # Define the workflow edges
        workflow.set_entry_point("read_email")
        
        workflow.add_edge("read_email", "classify_complaint")
        workflow.add_edge("classify_complaint", "determine_actions")
        workflow.add_edge("determine_actions", "generate_response")
        workflow.add_edge("generate_response", "check_validation")
        
        # Conditional routing based on validation requirement
        workflow.add_conditional_edges(
            "check_validation",
            self._should_validate,
            {
                "validate": "create_validation_request",
                "execute": "execute_actions"
            }
        )
        
        workflow.add_edge("create_validation_request", "save_complaint")
        workflow.add_edge("execute_actions", "save_complaint")
        workflow.add_edge("save_complaint", END)
        workflow.add_edge("handle_error", END)
        
        return workflow
    
    def _read_email_node(self, state: ComplaintState) -> ComplaintState:
        """Node for reading and parsing email"""
        try:
            state["processing_log"].append(f"[{datetime.now().isoformat()}] Starting email processing")
            
            email_data = self.mail_reader.process_email(
                state["email_content"],
                state["sender_email"], 
                state["subject"]
            )
            
            state["email_data"] = email_data
            state["processing_log"].append(f"[{datetime.now().isoformat()}] Email parsed successfully")
            
        except Exception as e:
            state["error_message"] = f"Email reading failed: {str(e)}"
            state["processing_log"].append(f"[{datetime.now().isoformat()}] ERROR: {str(e)}")
            
        return state
    
    def _classify_complaint_node(self, state: ComplaintState) -> ComplaintState:
        """Node for classifying complaint using AI"""
        try:
            if state.get("error_message"):
                return state
                
            classification = self.classifier.classify_complaint(state["email_data"])
            state["classification"] = classification
            state["processing_log"].append(
                f"[{datetime.now().isoformat()}] Complaint classified as: {classification['category']}"
            )
            
        except Exception as e:
            state["error_message"] = f"Classification failed: {str(e)}"
            state["processing_log"].append(f"[{datetime.now().isoformat()}] ERROR: {str(e)}")
            
        return state
    
    def _determine_actions_node(self, state: ComplaintState) -> ComplaintState:
        """Node for determining required actions"""
        try:
            if state.get("error_message"):
                return state
                
            proposed_actions = self._determine_actions(state["classification"])
            state["proposed_actions"] = proposed_actions
            state["processing_log"].append(
                f"[{datetime.now().isoformat()}] {len(proposed_actions)} actions proposed"
            )
            
        except Exception as e:
            state["error_message"] = f"Action determination failed: {str(e)}"
            state["processing_log"].append(f"[{datetime.now().isoformat()}] ERROR: {str(e)}")
            
        return state
    
    def _generate_response_node(self, state: ComplaintState) -> ComplaintState:
        """Node for generating response"""
        try:
            if state.get("error_message"):
                return state
                
            suggested_response = self.response_generator.generate_response(state["classification"])
            state["suggested_response"] = suggested_response
            state["processing_log"].append(
                f"[{datetime.now().isoformat()}] Response generated"
            )
            
        except Exception as e:
            state["error_message"] = f"Response generation failed: {str(e)}"
            state["processing_log"].append(f"[{datetime.now().isoformat()}] ERROR: {str(e)}")
            
        return state
    
    def _check_validation_node(self, state: ComplaintState) -> ComplaintState:
        """Node for checking if HR validation is required"""
        try:
            if state.get("error_message"):
                return state
                
            requires_validation = self.hr_supervisor.requires_validation(
                state["classification"], 
                state["proposed_actions"]
            )
            state["requires_validation"] = requires_validation
            state["processing_log"].append(
                f"[{datetime.now().isoformat()}] Validation required: {requires_validation}"
            )
            
        except Exception as e:
            state["error_message"] = f"Validation check failed: {str(e)}"
            state["processing_log"].append(f"[{datetime.now().isoformat()}] ERROR: {str(e)}")
            
        return state
    
    def _should_validate(self, state: ComplaintState) -> str:
        """Conditional function to determine next step"""
        if state.get("error_message"):
            return "handle_error"
        return "validate" if state["requires_validation"] else "execute"
    
    def _create_validation_request_node(self, state: ComplaintState) -> ComplaintState:
        """Node for creating HR validation request"""
        try:
            # Generate temporary complaint ID for validation
            complaint_id = f"TEMP_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            state["complaint_id"] = complaint_id
            
            validation_request = self.hr_supervisor.create_validation_request(
                complaint_id,
                state["classification"],
                state["proposed_actions"],
                state["suggested_response"]
            )
            state["validation_request"] = validation_request
            state["status"] = "pending_validation"
            state["processing_log"].append(
                f"[{datetime.now().isoformat()}] Validation request created"
            )
            
        except Exception as e:
            state["error_message"] = f"Validation request creation failed: {str(e)}"
            state["processing_log"].append(f"[{datetime.now().isoformat()}] ERROR: {str(e)}")
            
        return state
    
    def _execute_actions_node(self, state: ComplaintState) -> ComplaintState:
        """Node for executing approved actions"""
        try:
            if state.get("error_message"):
                return state
                
            action_results = []
            for action in state["proposed_actions"]:
                if action["type"] in ["find_candidate", "delete_candidate", "update_candidate"]:
                    result = self.db_handler.execute_action(action["type"], action["data"])
                    action_results.append(result)
            
            state["action_results"] = action_results
            state["status"] = "auto_resolved"
            state["processing_log"].append(
                f"[{datetime.now().isoformat()}] {len(action_results)} actions executed"
            )
            
        except Exception as e:
            state["error_message"] = f"Action execution failed: {str(e)}"
            state["processing_log"].append(f"[{datetime.now().isoformat()}] ERROR: {str(e)}")
            
        return state
    
    def _save_complaint_node(self, state: ComplaintState) -> ComplaintState:
        """Node for saving complaint to database"""
        try:
            if state.get("error_message"):
                state["status"] = "error"
            
            complaint_data = {
                "email_data": state["email_data"],
                "classification": state["classification"],
                "proposed_actions": state["proposed_actions"],
                "suggested_response": state["suggested_response"],
                "status": state["status"],
                "validation_request": state.get("validation_request"),
                "action_results": state.get("action_results", []),
                "hr_comments": state.get("hr_comments"),
                "processing_log": state["processing_log"]
            }
            
            if not state.get("complaint_id"):
                complaint_id = db_manager.add_complaint(complaint_data)
                state["complaint_id"] = complaint_id
            else:
                # Update existing complaint
                db_manager.update_complaint(state["complaint_id"], complaint_data)
            
            state["processing_log"].append(
                f"[{datetime.now().isoformat()}] Complaint saved: {state['complaint_id']}"
            )
            
        except Exception as e:
            state["error_message"] = f"Database save failed: {str(e)}"
            state["processing_log"].append(f"[{datetime.now().isoformat()}] ERROR: {str(e)}")
            
        return state
    
    def _handle_error_node(self, state: ComplaintState) -> ComplaintState:
        """Node for handling errors"""
        state["status"] = "error"
        state["processing_log"].append(
            f"[{datetime.now().isoformat()}] Processing failed: {state['error_message']}"
        )
        return state
    
    def _determine_actions(self, classification: Dict) -> List[Dict]:
        """Determine what actions should be taken based on classification"""
        actions = []
        category = classification["category"]
        extracted_info = classification.get("extracted_info", {})
        
        if category == "cin_duplicate":
            cin = extracted_info.get("cin")
            if cin:
                actions.append({
                    "type": "find_candidate",
                    "data": {"cin": cin},
                    "description": f"Look up candidate with CIN {cin}"
                })
                actions.append({
                    "type": "delete_candidate", 
                    "data": {"cin": cin},
                    "description": f"Delete existing registration for CIN {cin}"
                })
        
        elif category == "info_modification":
            cin = extracted_info.get("cin")
            if cin:
                actions.append({
                    "type": "find_candidate",
                    "data": {"cin": cin},
                    "description": f"Look up candidate with CIN {cin}"
                })
        
        # All categories get a response action
        actions.append({
            "type": "send_response",
            "data": {"method": "email"},
            "description": "Send automated response to candidate"
        })
        
        return actions
    
    def process_complaint(self, email_content: str, sender_email: str, subject: str) -> str:
        """Process a complaint through the LangGraph workflow"""
        try:
            # Initialize state
            initial_state = ComplaintState(
                complaint_id=None,
                email_content=email_content,
                sender_email=sender_email,
                subject=subject,
                email_data=None,
                classification=None,
                proposed_actions=None,
                suggested_response=None,
                validation_request=None,
                requires_validation=False,
                status="processing",
                action_results=None,
                hr_comments=None,
                processing_log=[],
                error_message=None
            )
            
            # Run the workflow
            final_state = self.app.invoke(initial_state)
            
            logger.info(f"Complaint processed: {final_state.get('complaint_id', 'UNKNOWN')}")
            return final_state.get("complaint_id", "ERROR")
            
        except Exception as e:
            logger.error(f"Workflow execution failed: {str(e)}")
            raise

# Global orchestrator instance
langgraph_orchestrator = LangGraphComplaintOrchestrator()
