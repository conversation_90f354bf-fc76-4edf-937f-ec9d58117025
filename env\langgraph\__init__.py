
"""
Minimal LangGraph implementation for compatibility
"""

class StateGraph:
    def __init__(self, state_type):
        self.state_type = state_type
        self.nodes = {}
        self.edges = []
        self.entry_point = None
    
    def add_node(self, name, func):
        self.nodes[name] = func
    
    def add_edge(self, from_node, to_node):
        self.edges.append((from_node, to_node))
    
    def add_conditional_edges(self, node, condition, mapping):
        # Simple implementation
        pass
    
    def set_entry_point(self, node):
        self.entry_point = node
    
    def compile(self):
        return CompiledGraph(self)

class CompiledGraph:
    def __init__(self, graph):
        self.graph = graph
    
    def invoke(self, state):
        # Simple sequential execution
        if self.graph.entry_point and self.graph.entry_point in self.graph.nodes:
            func = self.graph.nodes[self.graph.entry_point]
            return func(state)
        return state

END = "END"

__version__ = "0.0.1-minimal"
