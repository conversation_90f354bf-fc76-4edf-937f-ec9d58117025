# AI Team Orchestrator - Requirements
# Core dependencies for the multi-agent complaint management system

# Web Framework
flask>=3.1.1
werkzeug>=3.1.3
gunicorn>=23.0.0

# Database
flask-sqlalchemy>=3.1.1
mysql-connector-python>=8.0.33

# <PERSON> and <PERSON><PERSON><PERSON><PERSON>
groq>=0.26.0
openai>=1.82.1
langgraph>=0.0.55
langchain>=0.1.0
langchain-community>=0.0.20

# Email Processing
email-validator>=2.2.0
email-reply-parser>=0.5.12

# Utilities
python-dotenv>=1.0.0
schedule>=1.2.0

# Optional: Oracle support (uncomment if needed)
# cx-oracle>=8.3.0

# Optional: Enhanced email support (may need system dependencies)
# imaplib2>=3.6
# smtplib2>=0.2.1
