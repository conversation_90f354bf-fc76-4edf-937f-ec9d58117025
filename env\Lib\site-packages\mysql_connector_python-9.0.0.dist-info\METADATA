Metadata-Version: 2.1
Name: mysql-connector-python
Version: 9.0.0
Summary: MySQL driver written in Python
Home-page: http://dev.mysql.com/doc/connector-python/en/index.html
Author: Oracle and/or its affiliates
Author-email: 
License: GNU GPLv2 (with FOSS License Exception)
Download-URL: http://dev.mysql.com/downloads/connector/python/
Keywords: mysql db
Platform: UNKNOWN
Classifier: Development Status :: 5 - Production/Stable
Classifier: Environment :: Other Environment
Classifier: Intended Audience :: Developers
Classifier: Intended Audience :: Education
Classifier: Intended Audience :: Information Technology
Classifier: Intended Audience :: System Administrators
Classifier: License :: OSI Approved :: GNU General Public License (GPL)
Classifier: Operating System :: OS Independent
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Topic :: Database
Classifier: Topic :: Software Development
Classifier: Topic :: Software Development :: Libraries :: Application Frameworks
Classifier: Topic :: Software Development :: Libraries :: Python Modules
Requires-Python: >=3.8
Provides-Extra: dns-srv
Requires-Dist: dnspython ==2.6.1 ; extra == 'dns-srv'
Provides-Extra: fido2
Requires-Dist: fido2 ==1.1.2 ; extra == 'fido2'
Provides-Extra: gssapi
Requires-Dist: gssapi <=1.8.2,>=1.6.9 ; extra == 'gssapi'
Provides-Extra: telemetry
Requires-Dist: opentelemetry-api ==1.18.0 ; extra == 'telemetry'
Requires-Dist: opentelemetry-sdk ==1.18.0 ; extra == 'telemetry'
Requires-Dist: opentelemetry-exporter-otlp-proto-http ==1.18.0 ; extra == 'telemetry'


MySQL driver written in Python which does not depend on MySQL C client
libraries and implements the DB API v2.0 specification (PEP-249).


