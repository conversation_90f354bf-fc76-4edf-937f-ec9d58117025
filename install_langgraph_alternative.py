#!/usr/bin/env python3
"""
Alternative LangGraph Installation
Uses different approaches to install LangGraph
"""

import subprocess
import sys
import os
import tempfile
import zipfile
import urllib.request

def run_command(command, description):
    """Run a command and handle errors"""
    print(f"🔄 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} completed")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed: {e.stderr}")
        return False

def install_from_wheel():
    """Try to install from a specific wheel file"""
    print("🎯 Method: Installing from wheel file")
    
    # Try different wheel URLs (these are examples, you'd need actual URLs)
    wheel_urls = [
        "https://files.pythonhosted.org/packages/source/l/langgraph/langgraph-0.0.55.tar.gz",
        "https://files.pythonhosted.org/packages/source/l/langgraph/langgraph-0.0.40.tar.gz",
    ]
    
    for url in wheel_urls:
        try:
            print(f"   Trying: {url}")
            if run_command(f"{sys.executable} -m pip install {url}", f"Installing from {url}"):
                return True
        except:
            continue
    
    return False

def install_with_index_url():
    """Try installing with different index URLs"""
    print("🎯 Method: Using alternative index URLs")
    
    index_urls = [
        "https://pypi.org/simple/",
        "https://pypi.python.org/simple/",
        "https://test.pypi.org/simple/",
    ]
    
    for index_url in index_urls:
        cmd = f"{sys.executable} -m pip install --index-url {index_url} langgraph"
        if run_command(cmd, f"Installing with index {index_url}"):
            return True
    
    return False

def install_with_extra_index():
    """Try installing with extra index URLs"""
    print("🎯 Method: Using extra index URLs")
    
    cmd = f"{sys.executable} -m pip install --extra-index-url https://pypi.org/simple/ langgraph"
    return run_command(cmd, "Installing with extra index")

def install_dependencies_manually():
    """Install LangGraph dependencies manually and then try LangGraph"""
    print("🎯 Method: Manual dependency installation")
    
    # Install all known LangGraph dependencies
    dependencies = [
        "langchain-core>=0.1.0",
        "langchain>=0.1.0", 
        "pydantic>=2.0.0",
        "typing-extensions>=4.0.0",
        "aiohttp",
        "httpx",
        "orjson",
        "packaging",
    ]
    
    print("   Installing dependencies...")
    for dep in dependencies:
        run_command(f"{sys.executable} -m pip install '{dep}'", f"Installing {dep}")
    
    # Now try LangGraph
    return run_command(f"{sys.executable} -m pip install langgraph --no-deps", "Installing LangGraph without deps")

def create_minimal_langgraph():
    """Create a minimal LangGraph implementation for compatibility"""
    print("🎯 Method: Creating minimal LangGraph implementation")
    
    # Create a minimal langgraph package
    langgraph_code = '''
"""
Minimal LangGraph implementation for compatibility
"""

class StateGraph:
    def __init__(self, state_type):
        self.state_type = state_type
        self.nodes = {}
        self.edges = []
        self.entry_point = None
    
    def add_node(self, name, func):
        self.nodes[name] = func
    
    def add_edge(self, from_node, to_node):
        self.edges.append((from_node, to_node))
    
    def add_conditional_edges(self, node, condition, mapping):
        # Simple implementation
        pass
    
    def set_entry_point(self, node):
        self.entry_point = node
    
    def compile(self):
        return CompiledGraph(self)

class CompiledGraph:
    def __init__(self, graph):
        self.graph = graph
    
    def invoke(self, state):
        # Simple sequential execution
        if self.graph.entry_point and self.graph.entry_point in self.graph.nodes:
            func = self.graph.nodes[self.graph.entry_point]
            return func(state)
        return state

END = "END"

__version__ = "0.0.1-minimal"
'''
    
    try:
        # Find site-packages directory
        import site
        site_packages = site.getsitepackages()[0]
        
        # Create langgraph directory
        langgraph_dir = os.path.join(site_packages, 'langgraph')
        os.makedirs(langgraph_dir, exist_ok=True)
        
        # Write __init__.py
        init_file = os.path.join(langgraph_dir, '__init__.py')
        with open(init_file, 'w') as f:
            f.write(langgraph_code)
        
        # Create graph submodule
        graph_dir = os.path.join(langgraph_dir, 'graph')
        os.makedirs(graph_dir, exist_ok=True)
        
        graph_init = os.path.join(graph_dir, '__init__.py')
        with open(graph_init, 'w') as f:
            f.write('from .. import StateGraph, END\n__all__ = ["StateGraph", "END"]\n')
        
        print("✅ Minimal LangGraph implementation created")
        return True
        
    except Exception as e:
        print(f"❌ Failed to create minimal implementation: {e}")
        return False

def verify_installation():
    """Verify LangGraph installation"""
    try:
        import langgraph
        from langgraph.graph import StateGraph, END
        print("✅ LangGraph verification successful")
        return True
    except ImportError as e:
        print(f"❌ LangGraph verification failed: {e}")
        return False

def main():
    """Main installation process"""
    print("🚀 Alternative LangGraph Installation")
    print("=" * 40)
    
    methods = [
        ("Wheel installation", install_from_wheel),
        ("Alternative index URLs", install_with_index_url),
        ("Extra index URLs", install_with_extra_index),
        ("Manual dependencies", install_dependencies_manually),
        ("Minimal implementation", create_minimal_langgraph),
    ]
    
    for method_name, method_func in methods:
        print(f"\n🔄 Trying: {method_name}")
        if method_func():
            if verify_installation():
                print(f"🎉 Success with {method_name}!")
                return True
            else:
                print(f"⚠️  {method_name} completed but verification failed")
        else:
            print(f"❌ {method_name} failed")
    
    print("\n❌ All installation methods failed")
    print("\n💡 The system can still work without LangGraph using basic orchestration")
    print("   Run: python start.py")
    return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
